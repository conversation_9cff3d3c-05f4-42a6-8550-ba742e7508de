<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MediScanAI Pro - Analyse d'images médicales avancée</title>
    <link href="/static/css/tailwind-local.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <link href="/static/css/enhanced-styles.css" rel="stylesheet">
   <style>
        /* Styles pour la navigation professionnelle */

        /* Animation de scroll pour la navbar */
        @keyframes navSlideDown {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .nav-professional {
            animation: navSlideDown 0.5s ease-out;
        }
        .nav-link {
            position: relative;
            padding: 0.75rem 0;
        }

        .nav-indicator {
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 3px;
            background: linear-gradient(90deg, #06b6d4, #3b82f6, #8b5cf6);
            transition: width 0.3s ease;
            border-radius: 2px;
        }

        .nav-link:hover .nav-indicator {
            width: 100%;
        }

        /* Liens de navigation améliorés */
        .nav-link-enhanced {
            position: relative;
            padding: 0.75rem 1rem;
            font-weight: 500;
            color: #374151;
            transition: all 0.3s ease;
            border-radius: 0.75rem;
            overflow: hidden;
        }

        .nav-bg-effect {
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 0.75rem;
        }

        .nav-link-enhanced:hover {
            color: #1d4ed8;
            transform: translateY(-1px);
        }

        .nav-link-enhanced:hover .nav-bg-effect {
            opacity: 1;
        }

        .nav-professional {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-professional.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        /* Boutons de navigation stylisés */
        .btn-nav-primary {
            position: relative;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            overflow: hidden;
            box-shadow: 0 4px 14px rgba(59, 130, 246, 0.3);
        }

        .btn-nav-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .btn-nav-secondary {
            background: rgba(59, 130, 246, 0.1);
            color: #1d4ed8;
            border: 1px solid rgba(59, 130, 246, 0.2);
            padding: 0.75rem 1.25rem;
            border-radius: 0.75rem;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-nav-secondary:hover {
            background: rgba(59, 130, 246, 0.15);
            border-color: rgba(59, 130, 246, 0.3);
            transform: translateY(-1px);
        }

        /* Barre de progression de scroll */
        .scroll-progress-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: linear-gradient(90deg, #06b6d4, #3b82f6, #8b5cf6);
            width: 0%;
            transition: width 0.1s ease;
            z-index: 10;
        }

        /* Styles pour les badges et indicateurs */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            color: #059669;
        }

        .pro-badge {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
        }

        /* Effets de glow pour les éléments interactifs */
        .glow-effect:hover {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
        }

        .pulse-effect {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Styles pour le footer professionnel */
        .footer-professional {
            background: linear-gradient(135deg, #1f2937 0%, #111827 25%, #0f172a 50%, #1e293b 75%, #334155 100%);
        }

        /* Styles pour le footer enterprise ultra-professionnel */
        .footer-enterprise {
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 25%, #312e81 50%, #1e3a8a 75%, #1e40af 100%);
            position: relative;
        }

        .footer-enterprise::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 40% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .footer-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .footer-particle {
            position: absolute;
            background: rgba(6, 182, 212, 0.1);
            border-radius: 50%;
            animation: footerFloat 8s ease-in-out infinite;
        }

        .footer-particle-1 {
            width: 30px;
            height: 30px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .footer-particle-2 {
            width: 20px;
            height: 20px;
            top: 60%;
            left: 80%;
            animation-delay: 3s;
        }

        .footer-particle-3 {
            width: 25px;
            height: 25px;
            top: 80%;
            left: 30%;
            animation-delay: 6s;
        }

        .footer-particle-4 {
            width: 15px;
            height: 15px;
            top: 40%;
            left: 70%;
            animation-delay: 2s;
        }

        @keyframes footerFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.3;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 0.6;
            }
        }

        /* Particules enterprise améliorées */
        .footer-particles-enterprise {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .footer-particle-enterprise {
            position: absolute;
            border-radius: 50%;
            animation: enterpriseFloat 12s ease-in-out infinite;
            background: linear-gradient(45deg, rgba(6, 182, 212, 0.1), rgba(59, 130, 246, 0.1));
            backdrop-filter: blur(1px);
        }

        .footer-particle-enterprise.footer-particle-1 {
            width: 40px;
            height: 40px;
            top: 15%;
            left: 8%;
            animation-delay: 0s;
            background: linear-gradient(45deg, rgba(6, 182, 212, 0.15), rgba(59, 130, 246, 0.1));
        }

        .footer-particle-enterprise.footer-particle-2 {
            width: 25px;
            height: 25px;
            top: 65%;
            left: 85%;
            animation-delay: 4s;
            background: linear-gradient(45deg, rgba(139, 92, 246, 0.1), rgba(6, 182, 212, 0.15));
        }

        .footer-particle-enterprise.footer-particle-3 {
            width: 35px;
            height: 35px;
            top: 85%;
            left: 25%;
            animation-delay: 8s;
            background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
        }

        .footer-particle-enterprise.footer-particle-4 {
            width: 20px;
            height: 20px;
            top: 35%;
            left: 75%;
            animation-delay: 2s;
            background: linear-gradient(45deg, rgba(6, 182, 212, 0.1), rgba(34, 197, 94, 0.1));
        }

        .footer-particle-enterprise.footer-particle-5 {
            width: 30px;
            height: 30px;
            top: 50%;
            left: 50%;
            animation-delay: 6s;
            background: linear-gradient(45deg, rgba(139, 92, 246, 0.15), rgba(59, 130, 246, 0.1));
        }

        .footer-particle-enterprise.footer-particle-6 {
            width: 18px;
            height: 18px;
            top: 25%;
            left: 40%;
            animation-delay: 10s;
            background: linear-gradient(45deg, rgba(34, 197, 94, 0.1), rgba(6, 182, 212, 0.1));
        }

        @keyframes enterpriseFloat {
            0%, 100% {
                transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
                opacity: 0.2;
            }
            25% {
                transform: translateY(-15px) translateX(10px) rotate(90deg) scale(1.1);
                opacity: 0.4;
            }
            50% {
                transform: translateY(-30px) translateX(-5px) rotate(180deg) scale(0.9);
                opacity: 0.6;
            }
            75% {
                transform: translateY(-10px) translateX(-15px) rotate(270deg) scale(1.05);
                opacity: 0.3;
            }
        }

        .footer-link {
            color: #d1d5db;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
        }

        .footer-link:hover {
            color: #06b6d4;
            transform: translateX(4px);
        }

        /* Effets améliorés pour le footer enterprise */
        .footer-enterprise .footer-link {
            position: relative;
            overflow: hidden;
        }

        .footer-enterprise .footer-link::before {
            content: '';
            position: absolute;
            left: -100%;
            bottom: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #06b6d4, transparent);
            transition: left 0.5s ease;
        }

        .footer-enterprise .footer-link:hover::before {
            left: 100%;
        }

        /* Animations pour les badges de certification */
        .certification-badge {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .certification-badge:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
        }

        /* Effet de glow pour les éléments interactifs */
        .glow-on-hover {
            transition: all 0.3s ease;
        }

        .glow-on-hover:hover {
            box-shadow: 0 0 20px rgba(6, 182, 212, 0.4),
                        0 0 40px rgba(6, 182, 212, 0.2),
                        0 0 60px rgba(6, 182, 212, 0.1);
        }

        /* Animation pour les métriques */
        .metric-card {
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.1);
        }

        /* Responsive pour le footer enterprise */
        @media (max-width: 1024px) {
            .footer-enterprise .lg\:grid-cols-6 {
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
            }

            .footer-enterprise .lg\:col-span-2 {
                grid-column: span 2;
            }
        }

        @media (max-width: 768px) {
            .footer-enterprise .lg\:grid-cols-6 {
                grid-template-columns: 1fr;
                gap: 3rem;
            }

            .footer-enterprise .lg\:col-span-2 {
                grid-column: span 1;
            }

            .footer-particle-enterprise {
                display: none;
            }
        }

        .footer-contact-item {
            display: flex;
            align-items: center;
            space-x: 0.75rem;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .footer-contact-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(6, 182, 212, 0.3);
        }

        .footer-contact-item i {
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.75rem;
        }

        .social-link {
            width: 3rem;
            height: 3rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #d1d5db;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            background: rgba(6, 182, 212, 0.2);
            border-color: rgba(6, 182, 212, 0.4);
            color: #06b6d4;
            transform: translateY(-2px);
        }

        .border-gradient {
            background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.3), transparent);
            height: 1px;
        }

        /* Responsive pour le footer */
        @media (max-width: 1024px) {
            .footer-professional .lg\:col-span-2 {
                grid-column: span 1;
            }

            .footer-professional .lg\:grid-cols-5 {
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
            }
        }

        @media (max-width: 768px) {
            .footer-professional .lg\:grid-cols-5 {
                grid-template-columns: 1fr;
                gap: 3rem;
            }

            .footer-professional .lg\:flex-row {
                flex-direction: column;
            }

            .footer-professional .lg\:space-x-6 > * {
                margin-left: 0;
                margin-right: 0;
            }

            .footer-professional .lg\:space-y-0 {
                gap: 1rem;
            }

            .footer-particle {
                display: none;
            }
        }

:root {
    --primary: #4361ee;
    --secondary: #3f37c9;
    --accent: #4cc9f0;
    --dark: #1a1a2e;
    --light: #f8f9fa;
}

.gradient-bg {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
}

.gradient-text {
    background: linear-gradient(90deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.drop-zone {
    border: 2px dashed #93c5fd;
    transition: all 0.3s ease;
}

.drop-zone.active {
    border-color: var(--primary);
    background-color: rgba(67, 97, 238, 0.05);
}

.result-card {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.result-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(0.95); opacity: 0.8; }
    50% { transform: scale(1); opacity: 1; }
    100% { transform: scale(0.95); opacity: 0.8; }
}

.image-preview {
    max-height: 400px;
    object-fit: contain;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.condition-indicator {
    height: 8px;
    border-radius: 4px;
}

.floating-btn {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.floating-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2);
}

.chatbot-container {
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.chatbot-container.open {
    transform: translateY(0);
}

.chat-message {
    max-width: 80%;
    animation: fadeIn 0.3s ease;
    word-wrap: break-word;
    line-height: 1.4;
}

.chat-message.typing-indicator {
    animation: pulse 1.5s infinite;
}

.suggestion-btn {
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.suggestion-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Styles pour les messages de chat améliorés */
.chat-message code {
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.chat-message strong {
    font-weight: 600;
}

.chat-message em {
    font-style: italic;
}

/* Animation pour les indicateurs de statut */
#chatStatus {
    animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.feature-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: white;
    font-size: 2rem;
    box-shadow: 0 10px 15px -3px rgba(67, 97, 238, 0.3);
}

.model-selector {
    transition: all 0.3s ease;
    border-width: 2px;
    border-color: #e5e7eb;
}

.model-selector.active {
    border-color: var(--primary);
    background-color: rgba(67, 97, 238, 0.05);
    box-shadow: 0 4px 6px -1px rgba(67, 97, 238, 0.1), 0 2px 4px -1px rgba(67, 97, 238, 0.06);
}

/* Styles améliorés pour le chatbot */
.chatbot-container {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.chatbot-container.minimized {
    height: 60px;
    overflow: hidden;
}

.chat-message {
    animation: slideInUp 0.3s ease-out;
    transform-origin: bottom;
}

.chat-message.user-message {
    margin-left: auto;
    margin-right: 0;
    max-width: 80%;
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
    border-radius: 20px 20px 5px 20px;
}

.chat-message.assistant-message {
    margin-left: 0;
    margin-right: auto;
    max-width: 85%;
    border-radius: 20px 20px 20px 5px;
}

.chat-message.error-message {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border-radius: 20px;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--secondary), var(--primary));
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.typing-indicator {
    animation: fadeIn 0.3s ease-in;
}

/* Responsive design pour le chatbot */
@media (max-width: 768px) {
    .chatbot-container {
        width: calc(100vw - 2rem);
        right: 1rem;
        left: 1rem;
        bottom: 5rem;
    }
}

/* Améliorations pour les résultats d'analyse */
.analysis-connection-btn {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
}

.analysis-connection-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(16, 185, 129, 0.4);
}

.analysis-connected {
    border: 2px solid #10b981;
    background: rgba(16, 185, 129, 0.1);
}

    </style>
</head>
<body class="bg-gray-50 font-sans text-gray-800">
    <!-- Navigation Professionnelle -->
    <nav class="nav-professional fixed w-full z-50 top-0 transition-all duration-300" id="mainNav">
        <div class="container mx-auto px-6 py-3">
            <div class="flex justify-between items-center">
                <!-- Logo et branding amélioré -->
                <div class="flex items-center space-x-4">
                    <div class="relative group">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 rounded-2xl flex items-center justify-center shadow-xl transition-all duration-300 group-hover:scale-105">
                            <i class="fas fa-brain text-white text-xl"></i>
                        </div>
                        <!-- Badge de statut IA actif -->
                        <div class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-2 border-white flex items-center justify-center">
                            <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                        </div>
                        <!-- Effet de glow -->
                        <div class="absolute inset-0 bg-gradient-to-br from-blue-400 to-indigo-600 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-xl"></div>
                    </div>
                    <div class="flex flex-col">
                        <div class="flex items-center space-x-2">
                            <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-800 to-indigo-900 bg-clip-text text-transparent">MediScanAI</h1>
                            <!-- Badge Pro -->
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200">
                                <i class="fas fa-crown mr-1 text-yellow-500"></i>
                                Pro
                            </span>
                        </div>
                        <p class="text-xs text-gray-500 flex items-center space-x-1">
                            <span>Powered by</span>
                            <span class="font-semibold text-blue-600">Advanced AI</span>
                            <i class="fas fa-robot text-blue-500 text-xs"></i>
                        </p>
                    </div>
                </div>

                <!-- Navigation principale améliorée -->
                <div class="hidden lg:flex items-center space-x-8">
                    <a href="#features" class="nav-link-enhanced group">
                        <span class="relative z-10">Fonctionnalités</span>
                        <div class="nav-bg-effect"></div>
                    </a>
                    <a href="#how-it-works" class="nav-link-enhanced group">
                        <span class="relative z-10">Comment ça marche</span>
                        <div class="nav-bg-effect"></div>
                    </a>
                    <a href="#testimonials" class="nav-link-enhanced group">
                        <span class="relative z-10">Témoignages</span>
                        <div class="nav-bg-effect"></div>
                    </a>
                    <a href="#pricing" class="nav-link-enhanced group">
                        <span class="relative z-10">Tarifs</span>
                        <div class="nav-bg-effect"></div>
                    </a>
                </div>

                <!-- Actions de navigation améliorées -->
                <div class="hidden md:flex items-center space-x-3">
                    <!-- Indicateur de statut -->
                    <div class="flex items-center space-x-2 px-3 py-2 bg-green-50 rounded-full border border-green-200">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-xs font-medium text-green-700">Système opérationnel</span>
                    </div>

                    <!-- Boutons d'action -->
                    <button class="btn-nav-secondary group">
                        <i class="fas fa-user-md mr-2 group-hover:scale-110 transition-transform"></i>
                        Espace Médecin
                    </button>
                    <button class="btn-nav-primary group" onclick="document.getElementById('upload').scrollIntoView({behavior: 'smooth'})">
                        <i class="fas fa-play mr-2 group-hover:translate-x-1 transition-transform"></i>
                        Analyser maintenant
                        <div class="absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
                    </button>
                </div>

                <!-- Menu mobile amélioré -->
                <button id="mobileMenuBtn" class="lg:hidden relative p-3 rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 hover:from-blue-100 hover:to-indigo-100 transition-all duration-300 group">
                    <i class="fas fa-bars text-blue-700 group-hover:scale-110 transition-transform"></i>
                    <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-xl opacity-0 group-hover:opacity-10 transition-opacity"></div>
                </button>
            </div>
        </div>

        <!-- Menu mobile premium -->
        <div id="mobileMenu" class="hidden lg:hidden bg-white/98 backdrop-blur-xl border-t border-gray-200 shadow-xl">
            <div class="container mx-auto px-6 py-8">
                <!-- Statut système mobile -->
                <div class="flex items-center justify-center space-x-2 mb-6 p-3 bg-green-50 rounded-xl border border-green-200">
                    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span class="text-sm font-medium text-green-700">Système opérationnel</span>
                </div>

                <div class="flex flex-col space-y-3">
                    <a href="#features" class="flex items-center space-x-4 p-4 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 group">
                        <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <i class="fas fa-star text-white text-sm"></i>
                        </div>
                        <div>
                            <span class="font-semibold text-gray-800">Fonctionnalités</span>
                            <p class="text-xs text-gray-500">Découvrez nos capacités IA</p>
                        </div>
                    </a>

                    <a href="#how-it-works" class="flex items-center space-x-4 p-4 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 group">
                        <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <i class="fas fa-cogs text-white text-sm"></i>
                        </div>
                        <div>
                            <span class="font-semibold text-gray-800">Comment ça marche</span>
                            <p class="text-xs text-gray-500">Processus d'analyse</p>
                        </div>
                    </a>

                    <a href="#testimonials" class="flex items-center space-x-4 p-4 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 group">
                        <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <i class="fas fa-comments text-white text-sm"></i>
                        </div>
                        <div>
                            <span class="font-semibold text-gray-800">Témoignages</span>
                            <p class="text-xs text-gray-500">Avis des professionnels</p>
                        </div>
                    </a>

                    <a href="#pricing" class="flex items-center space-x-4 p-4 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 group">
                        <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <i class="fas fa-tag text-white text-sm"></i>
                        </div>
                        <div>
                            <span class="font-semibold text-gray-800">Tarifs</span>
                            <p class="text-xs text-gray-500">Plans et abonnements</p>
                        </div>
                    </a>

                    <div class="pt-6 border-t border-gray-200 mt-4">
                        <div class="flex flex-col space-y-4">
                            <button class="flex items-center justify-center space-x-2 w-full p-4 bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-xl font-semibold hover:from-gray-200 hover:to-gray-300 transition-all duration-300">
                                <i class="fas fa-user-md"></i>
                                <span>Espace Médecin</span>
                            </button>
                            <button class="flex items-center justify-center space-x-2 w-full p-4 bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-xl font-semibold hover:from-blue-700 hover:to-indigo-800 transition-all duration-300 shadow-lg" onclick="document.getElementById('upload').scrollIntoView({behavior: 'smooth'})">
                                <i class="fas fa-play"></i>
                                <span>Analyser maintenant</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Barre de progression de scroll -->
        <div class="scroll-progress-bar"></div>
    </nav>

    <!-- Hero Section Professionnel -->
    <section class="hero-professional relative overflow-hidden">
        <!-- Background avec gradient animé -->
        <div class="absolute inset-0 bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900"></div>
        <div class="absolute inset-0 bg-gradient-to-tr from-transparent via-blue-500/10 to-cyan-400/20"></div>

        <!-- Particules animées -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="floating-particles">
                <div class="particle particle-1"></div>
                <div class="particle particle-2"></div>
                <div class="particle particle-3"></div>
                <div class="particle particle-4"></div>
                <div class="particle particle-5"></div>
                <div class="particle particle-6"></div>
            </div>
        </div>

        <!-- Contenu principal -->
        <div class="relative z-10 min-h-screen flex items-center pt-20 pb-16">
            <div class="container mx-auto px-4">
                <div class="grid lg:grid-cols-2 gap-12 items-center">
                    <!-- Contenu textuel -->
                    <div class="text-white space-y-8" data-aos="fade-right" data-aos-duration="1000">
                        <!-- Badge de statut -->
                        <div class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20">
                            <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                            <span class="text-sm font-medium">IA Médicale de Nouvelle Génération</span>
                        </div>

                        <!-- Titre principal -->
                        <div class="space-y-4">
                            <h1 class="text-5xl lg:text-7xl font-bold leading-tight">
                                <span class="block">Révolutionnez</span>
                                <span class="block bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
                                    l'analyse médicale
                                </span>
                                <span class="block">avec l'IA</span>
                            </h1>
                            <p class="text-xl lg:text-2xl text-blue-100 leading-relaxed max-w-2xl">
                                Détection d'anomalies avec <span class="font-bold text-cyan-400">98.7% de précision</span>.
                                Accélérez vos diagnostics et améliorez les soins patients.
                            </p>
                        </div>

                        <!-- Statistiques -->
                        <div class="grid grid-cols-3 gap-6 py-6">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-cyan-400">98.7%</div>
                                <div class="text-sm text-blue-200">Précision</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-cyan-400">50k+</div>
                                <div class="text-sm text-blue-200">Analyses</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-cyan-400">24/7</div>
                                <div class="text-sm text-blue-200">Disponible</div>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="flex flex-col sm:flex-row gap-4">
                            <button class="btn-hero-primary group" onclick="document.getElementById('upload').scrollIntoView({behavior: 'smooth'})">
                                <span class="relative z-10 flex items-center justify-center">
                                    <i class="fas fa-play mr-2"></i>
                                    Commencer l'analyse
                                </span>
                                <div class="absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </button>
                            <button class="btn-hero-secondary group">
                                <span class="flex items-center justify-center">
                                    <i class="fas fa-video mr-2"></i>
                                    Voir la démo
                                </span>
                            </button>
                        </div>

                        <!-- Indicateurs de confiance -->
                        <div class="flex items-center space-x-6 pt-4">
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-shield-alt text-green-400"></i>
                                <span class="text-sm text-blue-200">Certifié médical</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-lock text-green-400"></i>
                                <span class="text-sm text-blue-200">RGPD conforme</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-award text-green-400"></i>
                                <span class="text-sm text-blue-200">ISO 27001</span>
                            </div>
                        </div>
                    </div>

                    <!-- Interface visuelle -->
                    <div class="relative" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
                        <!-- Carte principale -->
                        <div class="relative bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl">
                            <!-- Header de l'interface -->
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                                    <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                </div>
                                <div class="text-white/80 text-sm font-medium">MediScanAI Pro</div>
                            </div>

                            <!-- Simulation d'interface -->
                            <div class="space-y-4">
                                <!-- Image d'analyse simulée -->
                                <div class="relative bg-gray-900/50 rounded-2xl p-4 border border-white/10">
                                    <img src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                                         alt="Analyse médicale"
                                         class="w-full h-48 object-cover rounded-xl opacity-80">

                                    <!-- Overlay d'analyse -->
                                    <div class="absolute inset-4 rounded-xl border-2 border-cyan-400/60 bg-cyan-400/10">
                                        <div class="absolute top-2 left-2 bg-cyan-400 text-black px-2 py-1 rounded text-xs font-bold">
                                            Anomalie détectée
                                        </div>
                                    </div>
                                </div>

                                <!-- Résultats d'analyse -->
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-white/5 rounded-xl border border-white/10">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                            <span class="text-white text-sm">Analyse en cours...</span>
                                        </div>
                                        <div class="text-cyan-400 text-sm font-bold">87%</div>
                                    </div>

                                    <div class="flex items-center justify-between p-3 bg-green-400/10 rounded-xl border border-green-400/20">
                                        <div class="flex items-center space-x-3">
                                            <i class="fas fa-check-circle text-green-400"></i>
                                            <span class="text-white text-sm">Pneumonie détectée</span>
                                        </div>
                                        <div class="text-green-400 text-sm font-bold">94%</div>
                                    </div>

                                    <div class="flex items-center justify-between p-3 bg-yellow-400/10 rounded-xl border border-yellow-400/20">
                                        <div class="flex items-center space-x-3">
                                            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                                            <span class="text-white text-sm">Épanchement pleural</span>
                                        </div>
                                        <div class="text-yellow-400 text-sm font-bold">76%</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Éléments flottants -->
                        <div class="absolute -top-6 -right-6 bg-gradient-to-r from-cyan-400 to-blue-500 p-4 rounded-2xl shadow-xl animate-float">
                            <i class="fas fa-brain text-white text-2xl"></i>
                        </div>

                        <div class="absolute -bottom-6 -left-6 bg-gradient-to-r from-green-400 to-emerald-500 p-4 rounded-2xl shadow-xl animate-float" style="animation-delay: 1s">
                            <i class="fas fa-heartbeat text-white text-2xl"></i>
                        </div>

                        <div class="absolute top-1/2 -right-12 bg-gradient-to-r from-purple-400 to-pink-500 p-3 rounded-xl shadow-lg animate-float" style="animation-delay: 2s">
                            <i class="fas fa-microscope text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Indicateur de scroll -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60 animate-bounce">
            <div class="flex flex-col items-center space-y-2">
                <span class="text-sm">Découvrir</span>
                <i class="fas fa-chevron-down"></i>
            </div>
        </div>
    </section>

    <!-- Trusted By Section Améliorée -->
    <section class="py-16 bg-gradient-to-r from-gray-50 to-blue-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12" data-aos="fade-up">
                <p class="text-gray-600 mb-6 text-lg">Fait confiance par plus de <span class="font-bold text-blue-600">500+ professionnels</span> de santé</p>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8 items-center opacity-60">
                    <!-- Logos d'hôpitaux fictifs -->
                    <div class="flex items-center justify-center p-4 bg-white rounded-xl shadow-sm">
                        <div class="text-2xl font-bold text-gray-400">Hôpital Paris</div>
                    </div>
                    <div class="flex items-center justify-center p-4 bg-white rounded-xl shadow-sm">
                        <div class="text-2xl font-bold text-gray-400">Clinique Lyon</div>
                    </div>
                    <div class="flex items-center justify-center p-4 bg-white rounded-xl shadow-sm">
                        <div class="text-2xl font-bold text-gray-400">CHU Marseille</div>
                    </div>
                    <div class="flex items-center justify-center p-4 bg-white rounded-xl shadow-sm">
                        <div class="text-2xl font-bold text-gray-400">Centre Médical</div>
                    </div>
                </div>
            </div>

            <!-- Statistiques impressionnantes -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mt-12">
                <div class="text-center" data-aos="fade-up" data-aos-delay="100">
                    <div class="text-4xl font-bold text-blue-600 mb-2">50,000+</div>
                    <div class="text-gray-600">Analyses réalisées</div>
                </div>
                <div class="text-center" data-aos="fade-up" data-aos-delay="200">
                    <div class="text-4xl font-bold text-green-600 mb-2">98.7%</div>
                    <div class="text-gray-600">Précision moyenne</div>
                </div>
                <div class="text-center" data-aos="fade-up" data-aos-delay="300">
                    <div class="text-4xl font-bold text-purple-600 mb-2">500+</div>
                    <div class="text-gray-600">Médecins partenaires</div>
                </div>
                <div class="text-center" data-aos="fade-up" data-aos-delay="400">
                    <div class="text-4xl font-bold text-orange-600 mb-2">24/7</div>
                    <div class="text-gray-600">Support disponible</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Upload Section -->
    <section id="upload" class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-aos="fade-up">Analysez vos images médicales</h2>
            <p class="text-center text-gray-600 max-w-2xl mx-auto mb-12" data-aos="fade-up" data-aos-delay="100">
                Téléchargez vos images radiologiques, dermatologiques ou autres et obtenez une analyse détaillée en quelques secondes.
            </p>

            <div class="max-w-4xl mx-auto">
                <!-- Sélecteur de modèle -->
                <div class="mb-8" data-aos="fade-up" data-aos-delay="100">
                    <h3 class="text-xl font-semibold mb-4 text-center">Sélectionner le type d'analyse</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-w-6xl mx-auto">
                        <div id="brainModelSelector" class="border rounded-lg p-5 cursor-pointer transition-all duration-200 hover:bg-blue-50 model-selector active" data-model="brain">
                            <div class="flex items-center justify-center mb-4">
                                <i class="fas fa-brain text-4xl text-blue-600"></i>
                            </div>
                            <h4 class="font-medium text-center text-lg">Analyse cérébrale</h4>
                            <p class="text-sm text-gray-500 text-center mt-2">Détection de tumeurs cérébrales</p>
                        </div>
                        <div id="oralModelSelector" class="border rounded-lg p-5 cursor-pointer transition-all duration-200 hover:bg-blue-50 model-selector" data-model="oral">
                            <div class="flex items-center justify-center mb-4">
                                <i class="fas fa-tooth text-4xl text-blue-600"></i>
                            </div>
                            <h4 class="font-medium text-center text-lg">Analyse buccale</h4>
                            <p class="text-sm text-gray-500 text-center mt-2">Détection de maladies orales</p>
                        </div>
                        <div id="alzheimerModelSelector" class="border rounded-lg p-5 cursor-pointer transition-all duration-200 hover:bg-blue-50 model-selector" data-model="alzheimer">
                            <div class="flex items-center justify-center mb-4">
                                <i class="fas fa-brain text-4xl text-blue-600"></i>
                            </div>
                            <h4 class="font-medium text-center text-lg">Analyse Alzheimer</h4>
                            <p class="text-sm text-gray-500 text-center mt-2">Détection de troubles cognitifs</p>
                        </div>
                        <div id="fractureModelSelector" class="border rounded-lg p-5 cursor-pointer transition-all duration-200 hover:bg-blue-50 model-selector" data-model="fracture">
                            <div class="flex items-center justify-center mb-4">
                                <i class="fas fa-bone text-4xl text-blue-600"></i>
                            </div>
                            <h4 class="font-medium text-center text-lg">Analyse fractures</h4>
                            <p class="text-sm text-gray-500 text-center mt-2">Détection de fractures osseuses</p>
                        </div>
                    </div>
                </div>

                <div id="dropZone" class="drop-zone rounded-xl p-8 text-center cursor-pointer mb-8" data-aos="fade-up" data-aos-delay="200">
                    <div class="flex flex-col items-center justify-center py-12">
                        <i class="fas fa-cloud-upload-alt text-5xl text-blue-500 mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-700 mb-2">Glissez-déposez vos images ici</h3>
                        <p class="text-gray-500 mb-4">ou</p>
                        <label for="fileUpload" class="bg-blue-600 text-white font-medium py-2 px-6 rounded-full cursor-pointer hover:bg-blue-700 transition">
                            Parcourir vos fichiers
                        </label>
                        <input id="fileUpload" type="file" class="hidden" accept="image/*">
                    </div>
                </div>

                <!-- Selected Image Preview -->
                <div id="imagePreviewContainer" class="hidden mb-8" data-aos="fade-up">
                    <h3 class="text-xl font-semibold mb-4">Image sélectionnée</h3>
                    <div class="bg-white p-4 rounded-xl shadow-sm">
                        <img id="selectedImagePreview" src="" alt="Aperçu de l'image sélectionnée" class="image-preview mx-auto">
                    </div>
                    <button id="analyzeBtn" class="mt-4 bg-blue-600 text-white font-medium py-3 px-8 rounded-full hover:bg-blue-700 transition w-full md:w-auto">
                        <i id="analyzeIcon" class="fas fa-brain mr-2"></i> <span id="analyzeText">Analyser l'image cérébrale</span>
                    </button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div class="bg-white p-6 rounded-xl shadow-sm" data-aos="fade-up" data-aos-delay="100">
                        <h3 class="font-semibold text-lg mb-4 text-gray-800">Types d'images supportés</h3>
                        <ul class="space-y-3">
                            <li class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                <span>Radiographies (DICOM, JPEG, PNG)</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                <span>IRM et scanners</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                <span>Échographies</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                <span>Images dermatologiques</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                <span>Images ophtalmologiques</span>
                            </li>
                        </ul>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm" data-aos="fade-up" data-aos-delay="200">
                        <h3 class="font-semibold text-lg mb-4 text-gray-800">Conditions détectables</h3>
                        <ul class="space-y-3">
                            <li class="flex items-center">
                                <i class="fas fa-lungs text-blue-500 mr-3"></i>
                                <span>Pneumonie et anomalies pulmonaires</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-bone text-blue-500 mr-3"></i>
                                <span>Fractures osseuses</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-brain text-blue-500 mr-3"></i>
                                <span>Tumeurs et anomalies cérébrales</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-heart text-blue-500 mr-3"></i>
                                <span>Anomalies cardiaques</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-eye text-blue-500 mr-3"></i>
                                <span>Problèmes rétiniens</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold mb-4" data-aos="fade-up">Fonctionnalités avancées</h2>
                <p class="text-gray-600 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                    Notre plateforme combine la puissance de l'IA avec des outils conçus pour les professionnels de santé.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="feature-card p-6 rounded-xl" data-aos="fade-up">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="text-xl font-bold text-center mb-3">IA Multimodale</h3>
                    <p class="text-gray-600 text-center">
                        Des modèles spécialisés pour chaque type d'imagerie médicale, offrant des analyses précises adaptées à chaque discipline.
                    </p>
                </div>

                <div class="feature-card p-6 rounded-xl" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3 class="text-xl font-bold text-center mb-3">Analytique Avancée</h3>
                    <p class="text-gray-600 text-center">
                        Visualisations interactives et mesures quantitatives pour une évaluation approfondie des résultats.
                    </p>
                </div>

                <div class="feature-card p-6 rounded-xl" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <h3 class="text-xl font-bold text-center mb-3">Workflow Clinique</h3>
                    <p class="text-gray-600 text-center">
                        Intégration transparente avec les systèmes hospitaliers et outils de gestion des patients.
                    </p>
                </div>

                <div class="feature-card p-6 rounded-xl" data-aos="fade-up">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-history"></i>
                    </div>
                    <h3 class="text-xl font-bold text-center mb-3">Suivi Temporel</h3>
                    <p class="text-gray-600 text-center">
                        Comparaison d'images sur différentes périodes pour évaluer l'évolution des conditions.
                    </p>
                </div>

                <div class="feature-card p-6 rounded-xl" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="text-xl font-bold text-center mb-3">Sécurité Maximale</h3>
                    <p class="text-gray-600 text-center">
                        Chiffrement de bout en bout et conformité HIPAA pour protéger les données sensibles.
                    </p>
                </div>

                <div class="feature-card p-6 rounded-xl" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="text-xl font-bold text-center mb-3">Mobile Friendly</h3>
                    <p class="text-gray-600 text-center">
                        Accédez aux analyses et résultats depuis n'importe quel appareil, où que vous soyez.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold mb-4" data-aos="fade-up">Comment ça marche</h2>
                <p class="text-gray-600 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                    Obtenez des résultats précis en seulement 3 étapes simples
                </p>
            </div>

            <div class="max-w-4xl mx-auto">
                <div class="flex flex-col md:flex-row justify-between items-center mb-12" data-aos="fade-up">
                    <div class="md:w-1/3 text-center mb-8 md:mb-0">
                        <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-blue-800 font-bold text-xl">1</span>
                        </div>
                        <h3 class="font-bold text-lg mb-2">Téléchargez votre image</h3>
                        <p class="text-gray-600">Glissez-déposez ou sélectionnez votre image médicale</p>
                    </div>
                    <div class="hidden md:block">
                        <i class="fas fa-arrow-right text-gray-400 text-2xl"></i>
                    </div>
                    <div class="md:w-1/3 text-center mb-8 md:mb-0" data-aos="fade-up" data-aos-delay="100">
                        <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-blue-800 font-bold text-xl">2</span>
                        </div>
                        <h3 class="font-bold text-lg mb-2">Analyse par IA</h3>
                        <p class="text-gray-600">Notre système examine minutieusement chaque détail</p>
                    </div>
                    <div class="hidden md:block">
                        <i class="fas fa-arrow-right text-gray-400 text-2xl"></i>
                    </div>
                    <div class="md:w-1/3 text-center" data-aos="fade-up" data-aos-delay="200">
                        <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-blue-800 font-bold text-xl">3</span>
                        </div>
                        <h3 class="font-bold text-lg mb-2">Obtenez les résultats</h3>
                        <p class="text-gray-600">Recevez un rapport détaillé avec les détections</p>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-md overflow-hidden" data-aos="fade-up">
                    <div class="md:flex">
                        <div class="md:w-1/2 p-8 bg-gray-50">
                            <h3 class="text-xl font-semibold mb-4">Exemple de rapport d'analyse</h3>
                            <div class="space-y-6">
                                <div>
                                    <h4 class="font-medium text-gray-700 mb-2">Résumé</h4>
                                    <p class="text-gray-600">L'analyse a identifié deux zones d'intérêt avec des anomalies potentielles. La première montre des caractéristiques compatibles avec une pneumonie, tandis que la seconde révèle un petit épanchement pleural.</p>
                                </div>

                                <div>
                                    <h4 class="font-medium text-gray-700 mb-3">Détails des détections</h4>
                                    <div class="space-y-4">
                                        <div class="border-l-4 border-blue-500 pl-4">
                                            <div class="flex justify-between items-start mb-1">
                                                <h5 class="font-medium">Pneumonie</h5>
                                                <span class="text-sm font-medium text-green-600">87% de confiance</span>
                                            </div>
                                            <div class="flex items-center mb-2">
                                                <div class="condition-indicator bg-green-500" style="width: 87%"></div>
                                            </div>
                                            <p class="text-sm text-gray-600">Opacités alvéolaires dans le lobe inférieur droit, suggérant une pneumonie bactérienne.</p>
                                        </div>

                                        <div class="border-l-4 border-yellow-500 pl-4">
                                            <div class="flex justify-between items-start mb-1">
                                                <h5 class="font-medium">Épanchement pleural</h5>
                                                <span class="text-sm font-medium text-yellow-600">72% de confiance</span>
                                            </div>
                                            <div class="flex items-center mb-2">
                                                <div class="condition-indicator bg-yellow-500" style="width: 72%"></div>
                                            </div>
                                            <p class="text-sm text-gray-600">Petit épanchement visible à la base pulmonaire droite, nécessitant une évaluation complémentaire.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="md:w-1/2 p-8 flex items-center justify-center bg-gray-100">
                            <img src="https://images.unsplash.com/photo-1576091160399-112ba8d25d1d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
                                 alt="Exemple d'analyse"
                                 class="image-preview rounded-lg">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold mb-4" data-aos="fade-up">Ce qu'en disent les professionnels</h2>
                <p class="text-gray-600 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                    Des médecins et radiologues nous font confiance pour leurs diagnostics
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-gray-50 p-8 rounded-xl" data-aos="fade-up">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                            <i class="fas fa-user-md text-blue-600"></i>
                        </div>
                        <div>
                            <h4 class="font-bold">Dr. Sophie Martin</h4>
                            <p class="text-sm text-gray-500">Radiologue, Hôpital Paris</p>
                        </div>
                    </div>
                    <p class="text-gray-600">
                        "L'analyse automatique de MediScanAI Pro m'aide à identifier rapidement les zones d'intérêt sur les radiographies, ce qui améliore considérablement mon workflow."
                    </p>
                    <div class="mt-4 flex">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>

                <div class="bg-gray-50 p-8 rounded-xl" data-aos="fade-up" data-aos-delay="100">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                            <i class="fas fa-user-md text-blue-600"></i>
                        </div>
                        <div>
                            <h4 class="font-bold">Dr. Jean Dubois</h4>
                            <p class="text-sm text-gray-500">Pneumologue, Clinique Lyon</p>
                        </div>
                    </div>
                    <p class="text-gray-600">
                        "La détection précoce des anomalies pulmonaires est cruciale. MediScanAI Pro offre une seconde opinion précieuse dans mon processus diagnostique."
                    </p>
                    <div class="mt-4 flex">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star-half-alt text-yellow-400"></i>
                    </div>
                </div>

                <div class="bg-gray-50 p-8 rounded-xl" data-aos="fade-up" data-aos-delay="200">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                            <i class="fas fa-user-md text-blue-600"></i>
                        </div>
                        <div>
                            <h4 class="font-bold">Dr. Amélie Leroy</h4>
                            <p class="text-sm text-gray-500">Dermatologue, Centre Médical</p>
                        </div>
                    </div>
                    <p class="text-gray-600">
                        "Pour les lésions cutanées, l'analyse d'image assistée par IA permet un dépistage plus efficace. Un outil formidable pour la pratique quotidienne."
                    </p>
                    <div class="mt-4 flex">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold mb-4" data-aos="fade-up">Tarifs adaptés à vos besoins</h2>
                <p class="text-gray-600 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                    Choisissez le plan qui correspond à votre pratique médicale
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                <div class="bg-white p-8 rounded-xl shadow-sm border border-gray-200" data-aos="fade-up">
                    <h3 class="text-xl font-bold text-center mb-4">Essentiel</h3>
                    <div class="text-center mb-6">
                        <span class="text-4xl font-bold">49€</span>
                        <span class="text-gray-500">/mois</span>
                    </div>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>50 analyses/mois</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>Radiographie & Scanner</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>Rapports basiques</span>
                        </li>
                        <li class="flex items-center text-gray-400">
                            <i class="fas fa-times text-red-300 mr-2"></i>
                            <span>Support prioritaire</span>
                        </li>
                    </ul>
                    <button class="w-full bg-gray-100 text-gray-800 font-medium py-3 px-6 rounded-full hover:bg-gray-200 transition">
                        Commencer
                    </button>
                </div>

                <div class="bg-white p-8 rounded-xl shadow-lg border-2 border-blue-500 transform scale-105" data-aos="fade-up" data-aos-delay="100">
                    <div class="bg-blue-500 text-white py-1 px-4 rounded-full text-sm font-bold inline-block mb-4 -mt-10 mx-auto">
                        POPULAIRE
                    </div>
                    <h3 class="text-xl font-bold text-center mb-4">Professionnel</h3>
                    <div class="text-center mb-6">
                        <span class="text-4xl font-bold">99€</span>
                        <span class="text-gray-500">/mois</span>
                    </div>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>200 analyses/mois</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>Tous types d'images</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>Rapports détaillés</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>Support prioritaire</span>
                        </li>
                    </ul>
                    <button class="w-full bg-blue-600 text-white font-medium py-3 px-6 rounded-full hover:bg-blue-700 transition">
                        Choisir ce plan
                    </button>
                </div>

                <div class="bg-white p-8 rounded-xl shadow-sm border border-gray-200" data-aos="fade-up" data-aos-delay="200">
                    <h3 class="text-xl font-bold text-center mb-4">Institutionnel</h3>
                    <div class="text-center mb-6">
                        <span class="text-4xl font-bold">Contact</span>
                    </div>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>Analyses illimitées</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>Tous types d'images</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>Rapports avancés</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>Support dédié</span>
                        </li>
                    </ul>
                    <button class="w-full bg-gray-100 text-gray-800 font-medium py-3 px-6 rounded-full hover:bg-gray-200 transition">
                        Nous contacter
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Analysis Results (Initially hidden) -->
    <section id="resultsSection" class="py-16 bg-white hidden">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center mb-12">
                <h2 class="text-3xl font-bold">Résultats de l'analyse</h2>

                <!-- Sélecteur de langue -->
                <div id="languageSelector" class="hidden mb-6 bg-white rounded-lg shadow p-3 flex justify-center space-x-4">
                    <button type="button" data-lang="fr" class="lang-btn active bg-blue-50 text-blue-700 px-4 py-2 rounded-md text-sm font-medium transition-colors">

                        Français
                    </button>
                    <button type="button" data-lang="en" class="lang-btn px-4 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors">

                        English
                    </button>
                    <button type="button" data-lang="ar" class="lang-btn px-4 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors">
                        العربية
                    </button>
                </div>
            </div>

            <div id="resultsContainer" class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <h3 class="font-semibold text-lg mb-4 text-gray-800">Image analysée</h3>
                    <div class="bg-gray-100 p-4 rounded-lg flex justify-center">
                        <img id="analyzedImage" src="" alt="Image analysée" class="max-h-80 object-contain">
                    </div>
                    <div class="mt-4 flex justify-center space-x-3 flex-wrap gap-2">
                        <button id="connectChatBtn" class="analysis-connection-btn flex items-center">
                            <i class="fas fa-comments mr-2"></i> Discuter de cette analyse
                        </button>
                        <button id="newAnalysisBtn" class="bg-blue-600 text-white font-medium py-2 px-4 rounded-full hover:bg-blue-700 transition">
                            <i class="fas fa-redo mr-2"></i> Nouvelle analyse
                        </button>
                        <button id="downloadPdfBtn" class="bg-green-600 text-white font-medium py-2 px-4 rounded-full hover:bg-green-700 transition">
                            <i class="fas fa-file-pdf mr-2"></i> Télécharger PDF
                        </button>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="font-semibold text-lg text-gray-800">Résultats de la détection</h3>
                        <div id="analysisStatus" class="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">
                            <i class="fas fa-check-circle mr-1"></i>Analyse complète
                        </div>
                    </div>
                    <div class="space-y-6">
                        <div>
                            <h4 class="font-medium text-gray-700 mb-2">Résumé des détections</h4>
                            <div id="detectionSummary" class="text-gray-600">
                                <!-- Will be filled by JS -->
                            </div>
                        </div>

                        <div>
                            <h4 class="font-medium text-gray-700 mb-3">Conditions détectées</h4>
                            <div id="conditionsList" class="space-y-4">
                                <!-- Will be filled by JS -->
                            </div>
                        </div>

                        <!-- Zone d'information sur la connexion chat -->
                        <div id="chatConnectionInfo" class="hidden bg-blue-50 border border-blue-200 rounded-lg p-3">
                            <div class="flex items-center text-blue-700">
                                <i class="fas fa-info-circle mr-2"></i>
                                <span class="text-sm font-medium">Cette analyse est connectée au chat</span>
                            </div>
                            <p class="text-xs text-blue-600 mt-1">Vous pouvez poser des questions sur ces résultats dans le chat</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="gradient-bg text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-6" data-aos="fade-up">Prêt à révolutionner vos diagnostics ?</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                Essayez MediScanAI Pro gratuitement pendant 14 jours. Aucune carte de crédit requise.
            </p>
            <button class="bg-white text-blue-800 font-bold py-3 px-8 rounded-full hover:bg-blue-100 transition" data-aos="fade-up" data-aos-delay="200">
                Commencer l'essai gratuit
            </button>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="text-center mb-16">
                    <h2 class="text-3xl font-bold mb-4" data-aos="fade-up">Contactez-nous</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                        Des questions ? Notre équipe est là pour vous aider.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div data-aos="fade-up">
                        <form class="space-y-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Nom complet</label>
                                <input type="text" id="name" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                <input type="email" id="email" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">Sujet</label>
                                <input type="text" id="subject" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Message</label>
                                <textarea id="message" rows="4" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                            </div>
                            <button type="submit" class="bg-blue-600 text-white font-medium py-3 px-6 rounded-full hover:bg-blue-700 transition w-full">
                                Envoyer le message
                            </button>
                        </form>
                    </div>

                    <div data-aos="fade-up" data-aos-delay="100">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <h3 class="text-xl font-semibold mb-6">Informations de contact</h3>
                            <div class="space-y-6">
                                <div class="flex items-start">
                                    <div class="bg-blue-100 p-3 rounded-full mr-4">
                                        <i class="fas fa-map-marker-alt text-blue-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium">Adresse</h4>
                                        <p class="text-gray-600">123 Rue de la Santé, 75015 Paris, France</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="bg-blue-100 p-3 rounded-full mr-4">
                                        <i class="fas fa-phone-alt text-blue-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium">Téléphone</h4>
                                        <p class="text-gray-600">+33 1 23 45 67 89</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="bg-blue-100 p-3 rounded-full mr-4">
                                        <i class="fas fa-envelope text-blue-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium">Email</h4>
                                        <p class="text-gray-600"><EMAIL></p>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-8">
                                <h4 class="font-medium mb-4">Suivez-nous</h4>
                                <div class="flex space-x-4">
                                    <a href="#" class="bg-blue-100 text-blue-600 p-3 rounded-full hover:bg-blue-200 transition">
                                        <i class="fab fa-twitter"></i>
                                    </a>
                                    <a href="#" class="bg-blue-100 text-blue-600 p-3 rounded-full hover:bg-blue-200 transition">
                                        <i class="fab fa-linkedin-in"></i>
                                    </a>
                                    <a href="#" class="bg-blue-100 text-blue-600 p-3 rounded-full hover:bg-blue-200 transition">
                                        <i class="fab fa-facebook-f"></i>
                                    </a>
                                    <a href="#" class="bg-blue-100 text-blue-600 p-3 rounded-full hover:bg-blue-200 transition">
                                        <i class="fab fa-instagram"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer Ultra-Professionnel Entreprise -->
    <footer class="footer-enterprise relative overflow-hidden">
        <!-- Background sophistiqué avec multiple layers -->
        <div class="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-950 to-indigo-950"></div>
        <div class="absolute inset-0 bg-gradient-to-tr from-transparent via-blue-600/3 to-cyan-400/5"></div>
        <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-900/20 via-transparent to-transparent"></div>

        <!-- Grille de fond subtile -->
        <div class="absolute inset-0 opacity-[0.02]">
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0); background-size: 20px 20px;"></div>
        </div>

        <!-- Particules animées améliorées -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="footer-particles-enterprise">
                <div class="footer-particle-enterprise footer-particle-1"></div>
                <div class="footer-particle-enterprise footer-particle-2"></div>
                <div class="footer-particle-enterprise footer-particle-3"></div>
                <div class="footer-particle-enterprise footer-particle-4"></div>
                <div class="footer-particle-enterprise footer-particle-5"></div>
                <div class="footer-particle-enterprise footer-particle-6"></div>
            </div>
        </div>

        <!-- Ligne de séparation lumineuse -->
        <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-cyan-400/50 to-transparent"></div>

        <div class="relative z-10">
            <!-- Section de métriques en temps réel -->
            <div class="container mx-auto px-6 py-8">
                <div class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 mb-12 glow-on-hover">
                    <div class="grid grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="text-center group metric-card rounded-xl p-4">
                            <div class="flex items-center justify-center mb-2">
                                <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse mr-2"></div>
                                <span class="text-green-400 font-semibold text-sm">Système Opérationnel</span>
                            </div>
                            <div class="text-2xl font-bold text-white">99.98%</div>
                            <div class="text-sm text-gray-400">Disponibilité</div>
                        </div>
                        <div class="text-center group metric-card rounded-xl p-4">
                            <div class="flex items-center justify-center mb-2">
                                <i class="fas fa-chart-line text-cyan-400 mr-2"></i>
                                <span class="text-cyan-400 font-semibold text-sm">Analyses Aujourd'hui</span>
                            </div>
                            <div class="text-2xl font-bold text-white counter" data-target="1247">0</div>
                            <div class="text-sm text-gray-400">Images traitées</div>
                        </div>
                        <div class="text-center group metric-card rounded-xl p-4">
                            <div class="flex items-center justify-center mb-2">
                                <i class="fas fa-users text-blue-400 mr-2"></i>
                                <span class="text-blue-400 font-semibold text-sm">Utilisateurs Actifs</span>
                            </div>
                            <div class="text-2xl font-bold text-white counter" data-target="342">0</div>
                            <div class="text-sm text-gray-400">En ligne maintenant</div>
                        </div>
                        <div class="text-center group metric-card rounded-xl p-4">
                            <div class="flex items-center justify-center mb-2">
                                <i class="fas fa-clock text-yellow-400 mr-2"></i>
                                <span class="text-yellow-400 font-semibold text-sm">Temps Moyen</span>
                            </div>
                            <div class="text-2xl font-bold text-white">1.8s</div>
                            <div class="text-sm text-gray-400">Par analyse</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section principale du footer -->
            <div class="container mx-auto px-6 py-16">
                <div class="grid grid-cols-1 lg:grid-cols-6 gap-12">
                    <!-- Branding et description améliorée -->
                    <div class="lg:col-span-2 space-y-8">
                        <div class="flex items-center space-x-4">
                            <div class="relative group">
                                <div class="w-16 h-16 bg-gradient-to-br from-cyan-400 via-blue-500 to-indigo-600 rounded-3xl flex items-center justify-center shadow-2xl transition-all duration-500 group-hover:scale-110 group-hover:rotate-3">
                                    <i class="fas fa-brain text-white text-2xl"></i>
                                </div>
                                <div class="absolute inset-0 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-3xl opacity-0 group-hover:opacity-30 transition-all duration-500 blur-xl scale-110"></div>
                                <!-- Badge de certification -->
                                <div class="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-check text-white text-xs"></i>
                                </div>
                            </div>
                            <div>
                                <h3 class="text-3xl font-bold text-white mb-1">MediScanAI</h3>
                                <p class="text-cyan-400 font-semibold text-lg">Intelligence Médicale Avancée</p>
                                <div class="flex items-center space-x-2 mt-2">
                                    <span class="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full border border-green-500/30">Certifié ISO 27001</span>
                                    <span class="px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full border border-blue-500/30">HIPAA Compliant</span>
                                </div>
                            </div>
                        </div>

                        <p class="text-gray-300 leading-relaxed text-lg">
                            Révolutionnez vos diagnostics médicaux avec notre plateforme d'IA de pointe.
                            Analyse précise, rapide et fiable pour les professionnels de santé du monde entier.
                        </p>

                        <!-- Certifications et badges -->
                        <div class="space-y-4">
                            <h4 class="text-white font-bold text-sm uppercase tracking-wider">Certifications & Conformité</h4>
                            <div class="grid grid-cols-2 gap-3">
                                <div class="flex items-center space-x-2 p-3 bg-white/5 rounded-lg border border-white/10 certification-badge">
                                    <i class="fas fa-shield-alt text-green-400"></i>
                                    <span class="text-gray-300 text-sm">ISO 27001</span>
                                </div>
                                <div class="flex items-center space-x-2 p-3 bg-white/5 rounded-lg border border-white/10 certification-badge">
                                    <i class="fas fa-user-shield text-blue-400"></i>
                                    <span class="text-gray-300 text-sm">HIPAA</span>
                                </div>
                                <div class="flex items-center space-x-2 p-3 bg-white/5 rounded-lg border border-white/10 certification-badge">
                                    <i class="fas fa-lock text-purple-400"></i>
                                    <span class="text-gray-300 text-sm">SOC 2</span>
                                </div>
                                <div class="flex items-center space-x-2 p-3 bg-white/5 rounded-lg border border-white/10 certification-badge">
                                    <i class="fas fa-globe text-cyan-400"></i>
                                    <span class="text-gray-300 text-sm">GDPR</span>
                                </div>
                            </div>
                        </div>

                        <!-- Statistiques -->
                        <div class="grid grid-cols-3 gap-4 pt-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-cyan-400">50k+</div>
                                <div class="text-xs text-gray-400">Analyses</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-400">500+</div>
                                <div class="text-xs text-gray-400">Médecins</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-400">24/7</div>
                                <div class="text-xs text-gray-400">Support</div>
                            </div>
                        </div>

                        <!-- Newsletter premium -->
                        <div class="space-y-4 p-6 bg-gradient-to-br from-white/5 to-white/10 rounded-2xl border border-white/20 backdrop-blur-sm">
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-envelope-open text-cyan-400"></i>
                                <h4 class="text-white font-bold">Newsletter Professionnelle</h4>
                            </div>
                            <p class="text-gray-400 text-sm">Recevez les dernières innovations en IA médicale, études de cas et mises à jour produit</p>
                            <div class="flex space-x-2">
                                <input type="email" placeholder="<EMAIL>"
                                       class="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:bg-white/15 transition-all duration-300">
                                <button class="px-6 py-3 bg-gradient-to-r from-cyan-500 to-blue-500 text-white rounded-xl hover:from-cyan-600 hover:to-blue-600 transition-all duration-300 font-semibold shadow-lg hover:shadow-cyan-500/25">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                            <div class="flex items-center space-x-2 text-xs text-gray-500">
                                <i class="fas fa-shield-alt"></i>
                                <span>Vos données sont protégées. Pas de spam.</span>
                            </div>
                        </div>
                    </div>

                    <!-- Solutions -->
                    <div class="space-y-6">
                        <h4 class="text-white font-bold text-lg flex items-center">
                            <i class="fas fa-stethoscope mr-2 text-cyan-400"></i>
                            Solutions
                        </h4>
                        <ul class="space-y-3">
                            <li><a href="#" class="footer-link group">
                                <i class="fas fa-brain mr-2 text-blue-400 group-hover:text-cyan-400 transition-colors"></i>
                                Analyse cérébrale
                            </a></li>
                            <li><a href="#" class="footer-link group">
                                <i class="fas fa-lungs mr-2 text-green-400 group-hover:text-cyan-400 transition-colors"></i>
                                Imagerie pulmonaire
                            </a></li>
                            <li><a href="#" class="footer-link group">
                                <i class="fas fa-bone mr-2 text-orange-400 group-hover:text-cyan-400 transition-colors"></i>
                                Détection fractures
                            </a></li>
                            <li><a href="#" class="footer-link group">
                                <i class="fas fa-tooth mr-2 text-purple-400 group-hover:text-cyan-400 transition-colors"></i>
                                Analyse dentaire
                            </a></li>
                            <li><a href="#" class="footer-link group">
                                <i class="fas fa-eye mr-2 text-pink-400 group-hover:text-cyan-400 transition-colors"></i>
                                Ophtalmologie
                            </a></li>
                        </ul>
                    </div>

                    <!-- Ressources -->
                    <div class="space-y-6">
                        <h4 class="text-white font-bold text-lg flex items-center">
                            <i class="fas fa-book-medical mr-2 text-cyan-400"></i>
                            Ressources
                        </h4>
                        <ul class="space-y-3">
                            <li><a href="#" class="footer-link">Documentation API</a></li>
                            <li><a href="#" class="footer-link">Guides d'utilisation</a></li>
                            <li><a href="#" class="footer-link">Études de cas</a></li>
                            <li><a href="#" class="footer-link">Webinaires</a></li>
                            <li><a href="#" class="footer-link">Centre d'aide</a></li>
                            <li><a href="#" class="footer-link">Formation</a></li>
                        </ul>

                        <!-- Certifications -->
                        <div class="mt-8">
                            <h5 class="text-white font-semibold mb-3">Certifications</h5>
                            <div class="flex flex-wrap gap-2">
                                <span class="bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-xs font-medium border border-green-500/30">
                                    <i class="fas fa-shield-alt mr-1"></i>ISO 27001
                                </span>
                                <span class="bg-blue-500/20 text-blue-400 px-3 py-1 rounded-full text-xs font-medium border border-blue-500/30">
                                    <i class="fas fa-lock mr-1"></i>HIPAA
                                </span>
                                <span class="bg-purple-500/20 text-purple-400 px-3 py-1 rounded-full text-xs font-medium border border-purple-500/30">
                                    <i class="fas fa-certificate mr-1"></i>CE Medical
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Développeurs -->
                    <div class="space-y-6">
                        <h4 class="text-white font-bold text-lg flex items-center">
                            <i class="fas fa-code mr-2 text-cyan-400"></i>
                            Développeurs
                        </h4>
                        <ul class="space-y-3">
                            <li><a href="#" class="footer-link group">
                                <i class="fas fa-book mr-2 text-blue-400 group-hover:text-cyan-400 transition-colors"></i>
                                Documentation API
                            </a></li>
                            <li><a href="#" class="footer-link group">
                                <i class="fas fa-download mr-2 text-green-400 group-hover:text-cyan-400 transition-colors"></i>
                                SDK Python
                            </a></li>
                            <li><a href="#" class="footer-link group">
                                <i class="fas fa-terminal mr-2 text-purple-400 group-hover:text-cyan-400 transition-colors"></i>
                                CLI Tools
                            </a></li>
                            <li><a href="#" class="footer-link group">
                                <i class="fab fa-github mr-2 text-gray-400 group-hover:text-cyan-400 transition-colors"></i>
                                GitHub
                            </a></li>
                            <li><a href="#" class="footer-link group">
                                <i class="fas fa-flask mr-2 text-orange-400 group-hover:text-cyan-400 transition-colors"></i>
                                Sandbox
                            </a></li>
                        </ul>

                        <!-- Status API -->
                        <div class="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-white font-medium text-sm">Status API</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                    <span class="text-green-400 text-xs font-medium">Opérationnel</span>
                                </div>
                            </div>
                            <div class="text-gray-400 text-xs">Latence: 45ms | Uptime: 99.98%</div>
                        </div>
                    </div>

                    <!-- Support -->
                    <div class="space-y-6">
                        <h4 class="text-white font-bold text-lg flex items-center">
                            <i class="fas fa-headset mr-2 text-cyan-400"></i>
                            Support
                        </h4>

                        <!-- Contact rapide -->
                        <div class="space-y-4">
                            <div class="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10">
                                <div class="flex items-center space-x-3 mb-2">
                                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                    <span class="text-white font-medium text-sm">Support 24/7</span>
                                </div>
                                <p class="text-gray-300 text-xs">Assistance technique disponible</p>
                            </div>

                            <div class="space-y-3">
                                <a href="mailto:<EMAIL>" class="footer-contact-item group">
                                    <i class="fas fa-envelope text-cyan-400 group-hover:scale-110 transition-transform"></i>
                                    <div>
                                        <div class="text-white font-medium">Email</div>
                                        <div class="text-gray-400 text-sm"><EMAIL></div>
                                    </div>
                                </a>

                                <a href="tel:+33123456789" class="footer-contact-item group">
                                    <i class="fas fa-phone text-green-400 group-hover:scale-110 transition-transform"></i>
                                    <div>
                                        <div class="text-white font-medium">Téléphone</div>
                                        <div class="text-gray-400 text-sm">+33 1 23 45 67 89</div>
                                    </div>
                                </a>

                                <div class="footer-contact-item">
                                    <i class="fas fa-clock text-blue-400"></i>
                                    <div>
                                        <div class="text-white font-medium">Horaires</div>
                                        <div class="text-gray-400 text-sm">24h/24, 7j/7</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Réseaux sociaux -->
                        <div>
                            <h5 class="text-white font-semibold mb-4">Suivez-nous</h5>
                            <div class="flex space-x-3">
                                <a href="#" class="social-link group">
                                    <i class="fab fa-twitter group-hover:scale-110 transition-transform"></i>
                                </a>
                                <a href="#" class="social-link group">
                                    <i class="fab fa-linkedin-in group-hover:scale-110 transition-transform"></i>
                                </a>
                                <a href="#" class="social-link group">
                                    <i class="fab fa-youtube group-hover:scale-110 transition-transform"></i>
                                </a>
                                <a href="#" class="social-link group">
                                    <i class="fab fa-github group-hover:scale-110 transition-transform"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Barre de séparation avec gradient -->
            <div class="border-t border-gradient"></div>

            <!-- Footer bottom -->
            <div class="container mx-auto px-6 py-8">
                <div class="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
                    <div class="flex flex-col lg:flex-row items-center space-y-2 lg:space-y-0 lg:space-x-6">
                        <p class="text-gray-400 text-sm">
                            © 2025 MediScanAI. Tous droits réservés.
                        </p>
                        <div class="flex space-x-6 text-sm">
                            <a href="#" class="text-gray-400 hover:text-cyan-400 transition-colors">Politique de confidentialité</a>
                            <a href="#" class="text-gray-400 hover:text-cyan-400 transition-colors">Conditions d'utilisation</a>
                            <a href="#" class="text-gray-400 hover:text-cyan-400 transition-colors">Mentions légales</a>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <!-- Sélecteur de langue -->
                        <div class="relative">
                            <select class="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:border-cyan-400 transition-colors">
                                <option value="fr">🇫🇷 Français</option>
                                <option value="en">🇺🇸 English</option>
                                <option value="es">🇪🇸 Español</option>
                                <option value="de">🇩🇪 Deutsch</option>
                            </select>
                        </div>

                        <!-- Indicateur de statut -->
                        <div class="flex items-center space-x-2 bg-green-500/20 px-3 py-2 rounded-lg border border-green-500/30">
                            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                            <span class="text-green-400 text-sm font-medium">Tous systèmes opérationnels</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading State (Initially hidden) -->
    <section id="loadingSection" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white p-8 rounded-xl max-w-md w-full text-center">
            <div class="pulse-animation mb-6">
                <i class="fas fa-microscope text-5xl text-blue-600"></i>
            </div>
            <h3 class="text-xl font-semibold mb-2">Analyse en cours</h3>
            <p class="text-gray-600 mb-6">Notre IA examine minutieusement votre image médicale. Veuillez patienter...</p>
            <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div id="progressBar" class="bg-blue-600 h-2.5 rounded-full" style="width: 0%"></div>
            </div>
        </div>
    </section>

    <!-- Chatbot Button -->
    <button id="chatbotBtn" class="fixed bottom-6 right-6 bg-blue-600 text-white p-4 rounded-full shadow-lg hover:bg-blue-700 transition z-40 floating-btn">
        <i class="fas fa-comment-medical text-xl"></i>
    </button>

    <!-- Chatbot Container Amélioré -->
    <div id="chatbotContainer" class="fixed bottom-24 right-6 w-96 bg-white rounded-2xl shadow-2xl border border-gray-200 z-40 chatbot-container hidden transform transition-all duration-300 ease-in-out">
        <!-- En-tête du chatbot amélioré -->
        <div class="gradient-bg text-white p-4 rounded-t-2xl flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-robot text-sm"></i>
                </div>
                <div>
                    <h3 class="font-bold text-sm">Assistant MediScanAI</h3>
                    <div class="flex items-center space-x-2">
                        <span id="chatStatus" class="text-xs bg-green-500 text-white px-2 py-0.5 rounded-full flex items-center">
                            <div class="w-1.5 h-1.5 bg-white rounded-full mr-1 animate-pulse"></div>
                            En ligne
                        </span>
                        <span id="analysisIndicator" class="text-xs bg-purple-500 text-white px-2 py-0.5 rounded-full hidden">
                            <i class="fas fa-microscope mr-1"></i>Analyse liée
                        </span>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <button id="minimizeChatBtn" class="text-white hover:text-blue-200 text-sm transition-colors" title="Réduire">
                    <i class="fas fa-minus"></i>
                </button>
                <button id="clearChatBtn" class="text-white hover:text-blue-200 text-sm transition-colors" title="Effacer l'historique">
                    <i class="fas fa-trash"></i>
                </button>
                <button id="closeChatbot" class="text-white hover:text-blue-200 transition-colors">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Zone des messages améliorée -->
        <div id="chatbotMessages" class="p-4 h-96 overflow-y-auto bg-gradient-to-b from-gray-50 to-white custom-scrollbar">
            <div class="chat-message assistant-message bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 rounded-2xl p-4 mb-4 shadow-sm border border-blue-200">
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-robot text-white text-xs"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm leading-relaxed">Bonjour ! Je suis votre assistant médical IA. Je peux vous aider à comprendre vos analyses et répondre à vos questions médicales.</p>
                        <span class="text-xs opacity-70 mt-2 block">Assistant • Maintenant</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Zone de saisie améliorée -->
        <div class="p-4 border-t border-gray-200 bg-white rounded-b-2xl">
            <!-- Indicateur de frappe -->
            <div id="typingIndicator" class="hidden mb-3">
                <div class="flex items-center space-x-2 text-gray-500 text-sm">
                    <div class="flex space-x-1">
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                    </div>
                    <span>L'assistant réfléchit...</span>
                </div>
            </div>

            <!-- Zone de saisie principale -->
            <div class="flex items-end space-x-2 mb-3">
                <div class="flex-1 relative">
                    <textarea id="chatbotInput"
                             placeholder="Posez votre question médicale..."
                             class="w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-all duration-200 text-sm"
                             rows="1"
                             maxlength="500"></textarea>
                    <div class="absolute right-3 bottom-3 text-xs text-gray-400" id="charCount">0/500</div>
                </div>
                <button id="sendChatbotMsg"
                        class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-3 rounded-2xl hover:from-blue-700 hover:to-blue-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl">
                    <i class="fas fa-paper-plane text-sm"></i>
                </button>
            </div>

            <!-- Informations de session -->
            <div class="flex justify-between items-center text-xs text-gray-500">
                <div class="flex items-center space-x-3">
                    <span id="sessionInfo" class="flex items-center">
                        <i class="fas fa-circle text-green-500 mr-1" style="font-size: 6px;"></i>
                        Session active
                    </span>
                    <button id="modelTypeBtn" class="text-blue-600 hover:text-blue-800 transition-colors flex items-center space-x-1" title="Type de modèle actuel">
                        <i class="fas fa-brain"></i>
                        <span id="currentModelText">Cerveau</span>
                    </button>
                </div>
                <div class="flex items-center space-x-2">
                    <span id="messageCount" class="text-gray-400">0 messages</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Top Button -->
    <button id="backToTop" class="fixed bottom-24 left-6 bg-gray-800 text-white p-3 rounded-full shadow-lg hover:bg-gray-700 transition z-40 hidden">
        <i class="fas fa-arrow-up"></i>
    </button>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
   <script>

// Initialize AOS animations
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS if it exists
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });
    }

    // DOM Elements
    const dropZone = document.getElementById('dropZone');
    const fileUpload = document.getElementById('fileUpload');
    const loadingSection = document.getElementById('loadingSection');
    const resultsSection = document.getElementById('resultsSection');
    const analyzedImage = document.getElementById('analyzedImage');
    const detectionSummary = document.getElementById('detectionSummary');
    const conditionsList = document.getElementById('conditionsList');
    const newAnalysisBtn = document.getElementById('newAnalysisBtn');
    const downloadPdfBtn = document.getElementById('downloadPdfBtn');
    const progressBar = document.getElementById('progressBar');
    const imagePreviewContainer = document.getElementById('imagePreviewContainer');
    const selectedImagePreview = document.getElementById('selectedImagePreview');
    const analyzeBtn = document.getElementById('analyzeBtn');
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const mobileMenu = document.getElementById('mobileMenu');
    const chatbotBtn = document.getElementById('chatbotBtn');
    const chatbotContainer = document.getElementById('chatbotContainer');
    const closeChatbot = document.getElementById('closeChatbot');
    const chatbotMessages = document.getElementById('chatbotMessages');
    const chatbotInput = document.getElementById('chatbotInput');
    const sendChatbotMsg = document.getElementById('sendChatbotMsg');
    const backToTop = document.getElementById('backToTop');

    // Variables de chat améliorées
    let chatSessionId = null;
    let chatContext = "";
    let isTyping = false;
    let messageQueue = [];
    let messageCount = 0;
    let isMinimized = false;
    let analysisLinked = false;
    let typingTimeout = null;
    let cachedTranslations = {};

    // Variables globales pour stocker les résultats dans différentes langues
    let resultData = null;
    let currentLanguage = 'fr';
    let currentModelType = 'brain';
    let analyzedImageData = null;

    // Fonction pour initialiser le sélecteur de langue
    function initLanguageSelector() {
        const langButtons = document.querySelectorAll('.lang-btn');
        const languageSelector = document.getElementById('languageSelector');

        // Réinitialiser l'état actif
        langButtons.forEach(btn => {
            btn.classList.remove('active', 'bg-blue-50', 'text-blue-700');

            // Définir le bouton français comme actif par défaut
            if (btn.getAttribute('data-lang') === 'fr') {
                btn.classList.add('active', 'bg-blue-50', 'text-blue-700');
            }

            // Ajouter les écouteurs d'événements
            btn.addEventListener('click', function() {
                const lang = this.getAttribute('data-lang');

                // Mettre à jour l'apparence des boutons
                langButtons.forEach(b => b.classList.remove('active', 'bg-blue-50', 'text-blue-700'));
                this.classList.add('active', 'bg-blue-50', 'text-blue-700');

                // Changer la langue
                changeLanguage(lang);
            });
        });

        // Afficher le sélecteur de langue
        if (languageSelector) {
            languageSelector.classList.remove('hidden');
        }
    }

    // Fonction pour changer la langue des résultats
    function changeLanguage(lang) {
        if (currentLanguage === lang || !resultData) return;

        currentLanguage = lang;

        // Traduire les résultats
        translateResults(lang);
    }

    // Fonction pour traduire les résultats
    function translateResults(lang) {
        // Afficher un indicateur de chargement pendant la traduction
        const detectionSummary = document.getElementById('detectionSummary');
        const conditionsList = document.getElementById('conditionsList');

        if (detectionSummary) {
            detectionSummary.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin mr-2"></i>Traduction en cours...</div>';
        }

        // Appeler l'API pour traduire les résultats
        fetch('/api/translate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                data: resultData,
                target_language: lang
            }),
        })
        .then(response => response.json())
        .then(translatedData => {
            // Mettre à jour l'affichage avec les données traduites
            showModelResults(translatedData, analyzedImage.src);
        })
        .catch(error => {
            console.error('Error translating results:', error);
            // En cas d'erreur, revenir à la langue par défaut
            if (detectionSummary) {
                detectionSummary.innerHTML += '<div class="text-red-500 mt-2">Erreur de traduction. Affichage en langue originale.</div>';
            }
            setTimeout(() => showModelResults(resultData, analyzedImage.src), 1000);
        });
    }

    // Functions
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlight() {
        if (dropZone) dropZone.classList.add('active');
    }

    function unhighlight() {
        if (dropZone) dropZone.classList.remove('active');
    }

    function handleDrop(e) {
        preventDefaults(e);
        const dt = e.dataTransfer;
        const files = dt.files;
        handleFiles({ target: { files } });
    }

    function handleFiles(e) {
        const files = e.target.files;
        if (files && files.length) {
            const file = files[0];
            if (file.type.match('image.*')) {
                previewImage(file);
            } else {
                alert('Veuillez sélectionner une image valide (JPEG, PNG, etc.)');
            }
        }
    }

    function previewImage(file) {
        const reader = new FileReader();

        reader.onload = function(e) {
            if (selectedImagePreview) {
                selectedImagePreview.src = e.target.result;
                if (imagePreviewContainer) imagePreviewContainer.classList.remove('hidden');

                // Scroll to preview
                setTimeout(() => {
                    if (imagePreviewContainer) imagePreviewContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                }, 100);
            }
        };

        reader.readAsDataURL(file);
    }

    function analyzeImage() {
        if (!selectedImagePreview.src) {
            alert('Veuillez sélectionner une image à analyser.');
            return;
        }

        // Reset language to default
        currentLanguage = 'fr';

        // Show loading indicator
        if (loadingSection) loadingSection.classList.remove('hidden');

        // Get base64 image data
        const imageData = selectedImagePreview.src;

        // Simulate progress while waiting for API response
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 10;
            if (progress > 95) progress = 95; // Cap at 95% until we get actual results
            if (progressBar) progressBar.style.width = `${progress}%`;
        }, 100);

        // Send to API with the current model type
        fetch('/api/analyze', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                image: imageData,
                model_type: currentModelType
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Erreur réseau ou serveur');
            }
            return response.json();
        })
        .then(data => {
            // Complete progress bar
            clearInterval(interval);
            if (progressBar) progressBar.style.width = '100%';

            setTimeout(() => {
                // Hide loading indicator
                if (loadingSection) loadingSection.classList.add('hidden');

                // Stocker l'ID de session de chat si disponible
                if (data.chat_session_id) {
                    chatSessionId = data.chat_session_id;
                    console.log('Session de chat associée:', chatSessionId);

                    // Afficher l'indicateur d'analyse dans le chat
                    updateAnalysisIndicator(true);
                }

                // Display real results from model
                showModelResults(data, imageData);
            }, 500);
        })
        .catch(error => {
            console.error('Error:', error);
            clearInterval(interval);

            // Show error message
            if (loadingSection) loadingSection.classList.add('hidden');
            alert('Une erreur est survenue lors de l\'analyse. Veuillez réessayer.');
        });
    }

    function showModelResults(data, imageData) {
        // Stocker les données pour une utilisation ultérieure
        resultData = data;

        // Définir la langue actuelle
        currentLanguage = data.language || 'fr';

        console.log("Showing results in language:", currentLanguage);
        console.log("Result data:", data);

        // Set the analyzed image
        if (analyzedImage) analyzedImage.src = imageData;

        // Fonction pour corriger les problèmes d'encodage UTF-8
        const fixUtf8Encoding = (text) => {
            if (!text) return '';

            // Common UTF-8 encoding issues replacements
            const replacements = {
                'Ã©': 'é',
                'Ã¨': 'è',
                'Ãª': 'ê',
                'Ã«': 'ë',
                'Ã ': 'à',
                'Ã¢': 'â',
                'Ã®': 'î',
                'Ã¯': 'ï',
                'Ã´': 'ô',
                'Ã¶': 'ö',
                'Ã¹': 'ù',
                'Ã»': 'û',
                'Ã¼': 'ü',
                'Ã§': 'ç',
                'Ã‰': 'É',
                'Ãˆ': 'È',
                'ÃŠ': 'Ê',
                'Ã‹': 'Ë',
                'Ã€': 'À',
                'Ã‚': 'Â',
                'ÃŽ': 'Î',
                'Ã': 'Ï',
                'Ã"': 'Ô',
                'Ã–': 'Ö',
                'Ã™': 'Ù',
                'Ã›': 'Û',
                'Ãœ': 'Ü',
                'Ã‡': 'Ç'
            };

            let fixedText = text;
            for (const [encoded, decoded] of Object.entries(replacements)) {
                fixedText = fixedText.replace(new RegExp(encoded, 'g'), decoded);
            }

            return fixedText;
        };

        // Process text to fix encoding and clean JSON
        const processText = (text) => {
            if (!text) return '';

            // Remove JSON prefix if present
            let cleanText = text.replace(/```json\s*\{\s*"summary":\s*"/i, '');
            cleanText = cleanText.replace(/```json\s*\{\s*"recommendations":\s*"/i, '');

            // Remove trailing quotes and braces if present
            cleanText = cleanText.replace(/"\s*\}\s*```$/i, '');

            // Fix encoding issues
            return fixUtf8Encoding(cleanText);
        };

        // Fix encoding issues in data
        if (data.class_name) data.class_name = processText(data.class_name);
        if (data.description) data.description = processText(data.description);

        // Clean and fix analysis data
        if (data.analysis) {
            if (data.analysis.summary) {
                data.analysis.summary = processText(data.analysis.summary);
            }

            if (data.analysis.recommendations) {
                if (typeof data.analysis.recommendations === 'string') {
                    data.analysis.recommendations = processText(data.analysis.recommendations);
                } else if (Array.isArray(data.analysis.recommendations)) {
                    data.analysis.recommendations = data.analysis.recommendations.map(rec => processText(rec));
                } else if (typeof data.analysis.recommendations === 'object') {
                    for (const key in data.analysis.recommendations) {
                        data.analysis.recommendations[key] = processText(data.analysis.recommendations[key]);
                    }
                }
            }
        }

        // Vérifier si nous sommes en mode arabe pour appliquer RTL
        const isArabic = data.language === 'ar';

        // Appliquer la classe RTL au conteneur de résultats si nécessaire
        const resultsContainer = document.getElementById('resultsContainer');
        if (resultsContainer) {
            if (isArabic) {
                resultsContainer.setAttribute('data-lang', 'ar');
                resultsContainer.classList.add('rtl-text');
            } else {
                resultsContainer.setAttribute('data-lang', data.language || 'fr');
                resultsContainer.classList.remove('rtl-text');
            }
        }

        // Populate summary with real data
        if (detectionSummary) {
            let summaryHTML = '';

            if (data.analysis && data.analysis.summary) {
                summaryHTML = `
                    <p class="mb-3 ${isArabic ? 'rtl-text' : ''}">${data.analysis.summary}</p>
                `;
            } else {
                const confidenceText = isArabic ? 'الثقة' : 'confiance';
                summaryHTML = `
                    <p class="mb-3 ${isArabic ? 'rtl-text' : ''}">
                        ${isArabic ?
                            `تم إكمال التحليل بنسبة ${data.confidence}% من ${confidenceText} للفئة "${data.class_name}".` :
                            `Analyse complétée avec une confiance de ${data.confidence}% pour la classe "${data.class_name}".`
                        }
                    </p>
                `;
            }

            const disclaimerText = isArabic ?
                'هذه النتائج مقدمة لأغراض إعلامية فقط ويجب التحقق منها من قبل أخصائي رعاية صحية.' :
                'Ces résultats sont fournis à titre indicatif et doivent être validés par un professionnel de santé.';

            detectionSummary.innerHTML = `
                ${summaryHTML}
                <div class="bg-blue-50 text-blue-800 p-3 rounded-lg ${isArabic ? 'rtl-text' : ''}">
                    <i class="fas fa-info-circle ${isArabic ? 'ml-2' : 'mr-2'}"></i>
                    ${disclaimerText}
                </div>
                <div class="mt-4 flex gap-2">
                    <button id="openChatWithAnalysis" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition flex items-center">
                        <i class="fas fa-comment-medical mr-2"></i>
                        Discuter de cette analyse
                    </button>
                    <button id="shareAnalysis" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition flex items-center">
                        <i class="fas fa-share mr-2"></i>
                        Partager
                    </button>
                </div>
            `;
        }

        // Populate conditions list with real data
        if (conditionsList) {
            let conditionsHTML = '';

            // Main condition from model prediction
            const confidenceColor = data.confidence > 75 ? 'text-green-600' : data.confidence > 50 ? 'text-yellow-600' : 'text-red-600';
            const borderColor = data.confidence > 75 ? 'border-blue-500' : 'border-yellow-500';
            const barColor = data.confidence > 75 ? 'bg-green-500' : 'bg-yellow-500';

            const confidenceText = isArabic ? 'الثقة' : 'confiance';

            conditionsHTML += `
                <div class="border-l-4 ${borderColor} pl-4 mb-4">
                    <div class="flex justify-between items-start mb-1 ${isArabic ? 'flex-row-reverse' : ''}">
                        <h5 class="font-medium">${data.class_name}</h5>
                        <span class="text-sm font-medium ${confidenceColor} ${isArabic ? 'ml-2' : 'mr-2'}">
                            ${isArabic ? `${confidenceText} ${data.confidence}%` : `${data.confidence}% de ${confidenceText}`}
                        </span>
                    </div>
                    <div class="flex items-center mb-2">
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="condition-indicator ${barColor} h-2 rounded-full" style="width: ${data.confidence}%"></div>
                        </div>
                    </div>
                    <p class="text-sm text-gray-600 ${isArabic ? 'rtl-text' : ''}">${data.description || 'Aucune description disponible.'}</p>
                </div>
            `;

            // Add recommendations if available
            if (data.analysis && data.analysis.recommendations) {
                const recommendationsTitle = isArabic ? 'التوصيات' : 'Recommandations';

                // Vérifions si les recommandations sont un tableau ou une chaîne
                let recommendationsContent = '';

                if (Array.isArray(data.analysis.recommendations)) {
                    // Si c'est un tableau, créons une liste à puces
                    recommendationsContent = '<ul class="list-disc pl-5 space-y-1">';
                    data.analysis.recommendations.forEach(rec => {
                        recommendationsContent += `<li class="text-sm text-gray-600 ${isArabic ? 'rtl-text' : ''}">${rec}</li>`;
                    });
                    recommendationsContent += '</ul>';
                } else if (typeof data.analysis.recommendations === 'object') {
                    // Si c'est un objet avec des clés spécifiques
                    recommendationsContent = '<ul class="list-none space-y-3">';
                    for (const [key, value] of Object.entries(data.analysis.recommendations)) {
                        const title = key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' ');
                        recommendationsContent += `
                            <li>
                                <span class="font-medium text-blue-700 block mb-1">${title}:</span>
                                <p class="text-sm text-gray-600 ${isArabic ? 'rtl-text' : ''} pl-2 border-l-2 border-blue-200">${value}</p>
                            </li>
                        `;
                    }
                    recommendationsContent += '</ul>';
                } else {
                    // Si c'est une simple chaîne de caractères
                    recommendationsContent = `<p class="text-sm text-gray-600 ${isArabic ? 'rtl-text' : ''}">${data.analysis.recommendations}</p>`;
                }

                conditionsHTML += `
                    <div class="border-l-4 border-green-500 pl-4 mb-4">
                        <div class="flex justify-between items-start mb-2 ${isArabic ? 'flex-row-reverse' : ''}">
                            <h5 class="font-medium">${recommendationsTitle}</h5>
                        </div>
                        ${recommendationsContent}
                    </div>
                `;
            }

            conditionsList.innerHTML = conditionsHTML;
        }

        // Afficher le sélecteur de langue et mettre à jour le bouton actif
        initLanguageSelector();
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(btn => {
            if (btn.getAttribute('data-lang') === currentLanguage) {
                btn.classList.add('active', 'bg-blue-50', 'text-blue-700');
            } else {
                btn.classList.remove('active', 'bg-blue-50', 'text-blue-700');
            }
        });

        // Show results section
        if (resultsSection) resultsSection.classList.remove('hidden');

        // Ajouter les gestionnaires d'événements pour les nouveaux boutons
        setTimeout(() => {
            const openChatBtn = document.getElementById('openChatWithAnalysis');
            const shareBtn = document.getElementById('shareAnalysis');

            if (openChatBtn) {
                openChatBtn.addEventListener('click', function() {
                    openChatWithAnalysisContext();
                });
            }

            if (shareBtn) {
                shareBtn.addEventListener('click', function() {
                    shareAnalysisResults();
                });
            }
        }, 100);

        // Scroll to results
        setTimeout(() => {
            if (resultsSection) resultsSection.scrollIntoView({ behavior: 'smooth' });
        }, 200);
    }

    function resetAnalysis() {
        if (resultsSection) resultsSection.classList.add('hidden');
        if (imagePreviewContainer) imagePreviewContainer.classList.add('hidden');
        if (fileUpload) fileUpload.value = '';
        if (selectedImagePreview) selectedImagePreview.src = '';
        document.querySelector('html').scrollTo({ top: 0, behavior: 'smooth' });
    }

    function toggleMobileMenu() {
        if (mobileMenu) mobileMenu.classList.toggle('hidden');
    }

    function toggleChatbot() {
        if (chatbotContainer) {
            chatbotContainer.classList.toggle('hidden');
            chatbotContainer.classList.toggle('open');
        }
    }

    // Fonctions de chat améliorées
    async function sendChatMessage() {
        if (!chatbotInput || !chatbotMessages || isTyping) return;

        const message = chatbotInput.value.trim();
        if (!message) return;

        // Empêcher l'envoi de messages multiples
        isTyping = true;
        chatbotInput.disabled = true;
        sendChatbotMsg.disabled = true;
        updateChatStatus('typing');

        // Ajouter le message utilisateur avec timestamp
        addUserMessage(message);
        messageCount++;
        updateMessageCount();

        // Vider l'input et réinitialiser la hauteur
        chatbotInput.value = '';
        chatbotInput.style.height = 'auto';
        updateCharCount();

        // Afficher l'indicateur de frappe amélioré
        showTypingIndicator();

        try {
            // Appeler l'API de chat améliorée
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    session_id: chatSessionId,
                    model_type: currentModelType
                })
            });

            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status}`);
            }

            const data = await response.json();

            // Mettre à jour l'ID de session
            if (data.session_id) {
                chatSessionId = data.session_id;
            }

            // Supprimer l'indicateur de frappe
            hideTypingIndicator();

            // Ajouter la réponse de l'assistant
            if (data.response) {
                addAssistantMessage(data.response, data.status);
                messageCount++;
                updateMessageCount();
            }

            // Mettre à jour les informations de session
            if (data.message_count) {
                messageCount = data.message_count;
                updateMessageCount();
            }

            updateChatStatus('online');

        } catch (error) {
            console.error('Erreur de chat:', error);
            hideTypingIndicator();
            addErrorMessage('Désolé, je rencontre des difficultés techniques. Veuillez réessayer dans quelques instants.');
            updateChatStatus('error');
        } finally {
            // Réactiver l'interface
            isTyping = false;
            chatbotInput.disabled = false;
            sendChatbotMsg.disabled = false;
            chatbotInput.focus();
        }
    }

    function addUserMessage(message) {
        const userMsg = document.createElement('div');
        userMsg.className = 'chat-message user-message p-4 mb-4 shadow-sm';

        userMsg.innerHTML = `
            <div class="flex items-end justify-end space-x-2">
                <div class="flex-1 text-right">
                    <div class="text-sm leading-relaxed">${escapeHtml(message)}</div>
                    <span class="text-xs opacity-70 mt-1 block">Vous • ${getCurrentTime()}</span>
                </div>
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-user text-xs"></i>
                </div>
            </div>
        `;

        chatbotMessages.appendChild(userMsg);
        scrollToBottom();
    }

    function addAssistantMessage(message, status = 'success') {
        const aiMsg = document.createElement('div');
        const statusClass = status === 'success'
            ? 'assistant-message bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 border border-blue-200'
            : 'assistant-message bg-gradient-to-r from-yellow-100 to-yellow-50 text-yellow-800 border border-yellow-200';

        aiMsg.className = `chat-message ${statusClass} rounded-2xl p-4 mb-4 shadow-sm`;

        // Formater le message (supporter le markdown basique)
        const formattedMessage = formatMessage(message);

        aiMsg.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-robot text-white text-xs"></i>
                </div>
                <div class="flex-1">
                    <div class="text-sm leading-relaxed">${formattedMessage}</div>
                    <span class="text-xs opacity-70 mt-2 block">Assistant • ${getCurrentTime()}</span>
                </div>
            </div>
        `;

        chatbotMessages.appendChild(aiMsg);
        scrollToBottom();
    }

    function addErrorMessage(message) {
        const errorMsg = document.createElement('div');
        errorMsg.className = 'chat-message error-message rounded-2xl p-4 mb-4 shadow-sm';
        errorMsg.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-white text-xs"></i>
                </div>
                <div class="flex-1">
                    <div class="text-sm leading-relaxed">${escapeHtml(message)}</div>
                    <span class="text-xs opacity-70 mt-2 block">Erreur • ${getCurrentTime()}</span>
                </div>
            </div>
        `;
        chatbotMessages.appendChild(errorMsg);
        scrollToBottom();
    }

    // Nouvelles fonctions pour l'indicateur de frappe amélioré
    function showTypingIndicator() {
        const existingIndicator = document.getElementById('typingIndicator');
        if (existingIndicator) {
            existingIndicator.classList.remove('hidden');
        }
    }

    function hideTypingIndicator() {
        const existingIndicator = document.getElementById('typingIndicator');
        if (existingIndicator) {
            existingIndicator.classList.add('hidden');
        }
    }

    // Nouvelles fonctions utilitaires
    function updateChatStatus(status) {
        const chatStatus = document.getElementById('chatStatus');
        const statusIndicator = chatStatus.querySelector('.w-1\\.5');

        if (status === 'online') {
            chatStatus.className = 'text-xs bg-green-500 text-white px-2 py-0.5 rounded-full flex items-center';
            chatStatus.innerHTML = '<div class="w-1.5 h-1.5 bg-white rounded-full mr-1 animate-pulse"></div>En ligne';
        } else if (status === 'typing') {
            chatStatus.className = 'text-xs bg-blue-500 text-white px-2 py-0.5 rounded-full flex items-center';
            chatStatus.innerHTML = '<div class="w-1.5 h-1.5 bg-white rounded-full mr-1 animate-bounce"></div>Réfléchit...';
        } else if (status === 'error') {
            chatStatus.className = 'text-xs bg-red-500 text-white px-2 py-0.5 rounded-full flex items-center';
            chatStatus.innerHTML = '<div class="w-1.5 h-1.5 bg-white rounded-full mr-1"></div>Erreur';
        }
    }

    function updateMessageCount() {
        const messageCountElement = document.getElementById('messageCount');
        if (messageCountElement) {
            messageCountElement.textContent = `${messageCount} message${messageCount > 1 ? 's' : ''}`;
        }
    }

    function updateCharCount() {
        const charCount = document.getElementById('charCount');
        const length = chatbotInput.value.length;
        charCount.textContent = `${length}/500`;

        if (length > 450) {
            charCount.classList.add('text-red-500');
            charCount.classList.remove('text-gray-400');
        } else {
            charCount.classList.add('text-gray-400');
            charCount.classList.remove('text-red-500');
        }
    }

    // Fonction pour redimensionner automatiquement le textarea
    function autoResizeTextarea() {
        chatbotInput.style.height = 'auto';
        const maxHeight = 120; // 5 lignes environ
        const newHeight = Math.min(chatbotInput.scrollHeight, maxHeight);
        chatbotInput.style.height = newHeight + 'px';
    }

    function scrollToBottom() {
        if (chatbotMessages) {
            chatbotMessages.scrollTop = chatbotMessages.scrollHeight;
        }
    }

    function getCurrentTime() {
        return new Date().toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function formatMessage(message) {
        // Support basique du markdown
        return message
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n/g, '<br>')
            .replace(/`(.*?)`/g, '<code class="bg-gray-200 px-1 rounded">$1</code>');
    }

    // Fonction pour connecter l'analyse au chat
    function connectAnalysisToChat(analysisData, imageInfo) {
        if (analysisData) {
            analysisLinked = true;

            // Afficher l'indicateur d'analyse liée
            const analysisIndicator = document.getElementById('analysisIndicator');
            if (analysisIndicator) {
                analysisIndicator.classList.remove('hidden');
            }

            // Ajouter un message système pour informer de la connexion
            const systemMsg = document.createElement('div');
            systemMsg.className = 'chat-message analysis-connected rounded-2xl p-3 mb-4 text-center';
            systemMsg.innerHTML = `
                <div class="flex items-center justify-center space-x-2 text-green-700">
                    <i class="fas fa-link"></i>
                    <span class="text-sm font-medium">Analyse d'image connectée au chat</span>
                </div>
                <p class="text-xs text-green-600 mt-1">Je peux maintenant discuter de vos résultats d'analyse</p>
            `;

            chatbotMessages.appendChild(systemMsg);
            scrollToBottom();

            // Mettre à jour le placeholder
            chatbotInput.placeholder = "Posez vos questions sur l'analyse...";
        }
    }

    // Fonction pour minimiser/maximiser le chat
    function toggleChatMinimize() {
        isMinimized = !isMinimized;
        const container = document.getElementById('chatbotContainer');
        const minimizeBtn = document.getElementById('minimizeChatBtn');

        if (isMinimized) {
            container.classList.add('minimized');
            minimizeBtn.innerHTML = '<i class="fas fa-plus"></i>';
            minimizeBtn.title = 'Agrandir';
        } else {
            container.classList.remove('minimized');
            minimizeBtn.innerHTML = '<i class="fas fa-minus"></i>';
            minimizeBtn.title = 'Réduire';
        }
    }

    // Fonction pour effacer l'historique du chat améliorée
    async function clearChatHistory() {
        if (!chatSessionId) return;

        try {
            const response = await fetch('/api/chat/session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'clear',
                    session_id: chatSessionId
                })
            });

            if (response.ok) {
                // Réinitialiser les variables
                messageCount = 0;
                analysisLinked = false;

                // Masquer l'indicateur d'analyse
                const analysisIndicator = document.getElementById('analysisIndicator');
                if (analysisIndicator) {
                    analysisIndicator.classList.add('hidden');
                }

                // Vider l'interface avec le nouveau design
                chatbotMessages.innerHTML = `
                    <div class="chat-message assistant-message bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 rounded-2xl p-4 mb-4 shadow-sm border border-blue-200">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-robot text-white text-xs"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm leading-relaxed">Bonjour ! Je suis votre assistant médical IA. Je peux vous aider à comprendre vos analyses et répondre à vos questions médicales.</p>
                                <span class="text-xs opacity-70 mt-2 block">Assistant • Maintenant</span>
                            </div>
                        </div>
                    </div>
                `;

                // Mettre à jour les compteurs
                updateMessageCount();
                updateChatStatus('online');

                // Réinitialiser le placeholder
                chatbotInput.placeholder = "Posez votre question médicale...";

                console.log('Historique du chat effacé');
            }
        } catch (error) {
            console.error('Erreur lors de l\'effacement de l\'historique:', error);
        }
    }

    // Fonction pour ouvrir le chat avec le contexte d'analyse
    function openChatWithAnalysisContext() {
        // Ouvrir le chatbot
        if (chatbotContainer && chatbotContainer.classList.contains('hidden')) {
            toggleChatbot();
        }

        // Si nous avons des données d'analyse et une session
        if (resultData && chatSessionId) {
            // Ajouter un message automatique sur l'analyse
            const analysisMessage = createAnalysisMessage(resultData);

            // Ajouter le message à l'interface
            const systemMsg = document.createElement('div');
            systemMsg.className = 'chat-message bg-green-100 text-green-800 rounded-lg p-3 mb-3';
            systemMsg.innerHTML = `
                <div class="text-sm">
                    <i class="fas fa-microscope mr-2"></i>
                    <strong>Analyse d'image disponible</strong>
                    <p class="mt-2">${analysisMessage}</p>
                    <p class="mt-2 text-xs opacity-70">Vous pouvez maintenant me poser des questions sur cette analyse.</p>
                </div>
                <span class="text-xs opacity-70 mt-2 block">${getCurrentTime()}</span>
            `;

            if (chatbotMessages) {
                chatbotMessages.appendChild(systemMsg);
                scrollToBottom();
            }

            // Mettre le focus sur l'input
            if (chatbotInput) {
                chatbotInput.focus();
                chatbotInput.placeholder = "Posez-moi des questions sur votre analyse...";
            }

            // Masquer les suggestions car nous avons un contexte
            const suggestionsContainer = document.getElementById('chatSuggestions');
            if (suggestionsContainer) {
                suggestionsContainer.style.display = 'none';
            }
        } else {
            // Pas de données d'analyse, ouvrir normalement
            if (chatbotInput) {
                chatbotInput.focus();
            }
        }
    }

    // Fonction pour créer un message d'analyse
    function createAnalysisMessage(data) {
        let message = '';

        if (data.class_name) {
            message += `Condition détectée: ${data.class_name}`;
        }

        if (data.confidence) {
            message += ` (confiance: ${data.confidence}%)`;
        }

        if (data.description) {
            message += `. ${data.description}`;
        }

        return message || 'Analyse d\'image effectuée';
    }

    // Fonction pour mettre à jour l'indicateur d'analyse
    function updateAnalysisIndicator(hasAnalysis) {
        const indicator = document.getElementById('analysisIndicator');
        if (indicator) {
            if (hasAnalysis) {
                indicator.classList.remove('hidden');
                indicator.title = 'Une analyse d\'image est disponible pour cette conversation';
            } else {
                indicator.classList.add('hidden');
            }
        }
    }

    // Fonction pour partager les résultats d'analyse
    function shareAnalysisResults() {
        if (!resultData) {
            alert('Aucune analyse à partager');
            return;
        }

        // Créer un résumé partageable
        const shareText = `Résultats MediScanAI:\n` +
            `Condition: ${resultData.class_name || 'Non spécifiée'}\n` +
            `Confiance: ${resultData.confidence || 0}%\n` +
            `Description: ${resultData.description || 'Aucune description'}\n\n` +
            `Généré par MediScanAI - ${new Date().toLocaleDateString()}`;

        // Utiliser l'API Web Share si disponible
        if (navigator.share) {
            navigator.share({
                title: 'Résultats MediScanAI',
                text: shareText
            }).catch(err => {
                console.log('Erreur lors du partage:', err);
                fallbackShare(shareText);
            });
        } else {
            fallbackShare(shareText);
        }
    }

    // Fonction de partage de secours
    function fallbackShare(text) {
        // Copier dans le presse-papiers
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                alert('Résultats copiés dans le presse-papiers!');
            }).catch(() => {
                showShareModal(text);
            });
        } else {
            showShareModal(text);
        }
    }

    // Fonction pour afficher une modal de partage
    function showShareModal(text) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white p-6 rounded-lg max-w-md w-full mx-4">
                <h3 class="text-lg font-bold mb-4">Partager les résultats</h3>
                <textarea class="w-full h-32 p-3 border rounded-lg resize-none" readonly>${text}</textarea>
                <div class="flex gap-2 mt-4">
                    <button onclick="this.parentElement.parentElement.parentElement.remove()"
                            class="flex-1 bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                        Fermer
                    </button>
                    <button onclick="navigator.clipboard.writeText('${text.replace(/'/g, "\\'")}'').then(() => alert('Copié!')); this.parentElement.parentElement.parentElement.remove()"
                            class="flex-1 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        Copier
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Fermer en cliquant à l'extérieur
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    function toggleBackToTop() {
        if (backToTop) {
            if (window.pageYOffset > 300) {
                backToTop.classList.remove('hidden');
            } else {
                backToTop.classList.add('hidden');
            }
        }
    }

    function scrollToTop() {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    // Fonction pour télécharger le rapport PDF
    async function downloadPdfReport() {
        if (!resultData || !analyzedImage || !analyzedImage.src) {
            alert('Aucun résultat disponible pour générer un PDF.');
            return;
        }

        try {
            // Afficher un indicateur de chargement
            downloadPdfBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Génération...';
            downloadPdfBtn.disabled = true;

            // Appeler l'API pour générer le PDF
            const response = await fetch('/api/generate-pdf', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    data: resultData,
                    image: analyzedImage.src,
                    site_name: 'MediScanAI Pro'
                }),
            });

            if (!response.ok) {
                throw new Error('Erreur lors de la génération du PDF');
            }

            // Convertir la réponse en blob
            const blob = await response.blob();

            // Créer un URL pour le blob
            const url = window.URL.createObjectURL(blob);

            // Créer un lien de téléchargement
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `rapport_medical_${new Date().toISOString().slice(0, 10)}.pdf`;

            // Ajouter le lien au document
            document.body.appendChild(a);

            // Cliquer sur le lien pour déclencher le téléchargement
            a.click();

            // Nettoyer
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            console.error('Erreur:', error);
            alert('Une erreur est survenue lors de la génération du PDF. Veuillez réessayer.');
        } finally {
            // Restaurer le bouton
            downloadPdfBtn.innerHTML = '<i class="fas fa-file-pdf mr-2"></i> Télécharger PDF';
            downloadPdfBtn.disabled = false;
        }
    }

    // Event Listeners
    if (dropZone) {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        dropZone.addEventListener('drop', handleDrop, false);
    }

    // Initialiser les sélecteurs de modèle
    function initModelSelectors() {
        const modelSelectors = document.querySelectorAll('.model-selector');
        const analyzeIcon = document.getElementById('analyzeIcon');
        const analyzeText = document.getElementById('analyzeText');

        modelSelectors.forEach(selector => {
            selector.addEventListener('click', function() {
                // Mettre à jour l'apparence des sélecteurs
                modelSelectors.forEach(s => s.classList.remove('active'));
                this.classList.add('active');

                // Mettre à jour le type de modèle courant
                currentModelType = this.getAttribute('data-model');

                // Mettre à jour l'icône et le texte du bouton d'analyse
                if (currentModelType === 'oral') {
                    if (analyzeIcon) analyzeIcon.className = 'fas fa-tooth mr-2';
                    if (analyzeText) analyzeText.textContent = 'Analyser l\'image buccale';
                } else if (currentModelType === 'alzheimer') {
                    if (analyzeIcon) analyzeIcon.className = 'fas fa-brain mr-2';
                    if (analyzeText) analyzeText.textContent = 'Analyser l\'image Alzheimer';
                } else if (currentModelType === 'fracture') {
                    if (analyzeIcon) analyzeIcon.className = 'fas fa-bone mr-2';
                    if (analyzeText) analyzeText.textContent = 'Analyser l\'image de fracture';
                } else {
                    if (analyzeIcon) analyzeIcon.className = 'fas fa-brain mr-2';
                    if (analyzeText) analyzeText.textContent = 'Analyser l\'image cérébrale';
                }
            });
        });
    }

    // Initialiser les sélecteurs de modèle au chargement
    initModelSelectors();

    // Gestionnaires d'événements pour les nouvelles fonctionnalités de chat améliorées
    const clearChatBtn = document.getElementById('clearChatBtn');
    const modelTypeBtn = document.getElementById('modelTypeBtn');
    const minimizeChatBtn = document.getElementById('minimizeChatBtn');

    // Bouton pour effacer l'historique du chat
    if (clearChatBtn) {
        clearChatBtn.addEventListener('click', function() {
            if (confirm('Voulez-vous vraiment effacer l\'historique du chat ?')) {
                clearChatHistory();
            }
        });
    }

    // Bouton pour minimiser/maximiser le chat
    if (minimizeChatBtn) {
        minimizeChatBtn.addEventListener('click', toggleChatMinimize);
    }

    // Bouton pour changer le type de modèle
    if (modelTypeBtn) {
        modelTypeBtn.addEventListener('click', function() {
            // Cycle entre les types de modèles
            const modelTypes = ['brain', 'oral', 'alzheimer', 'fracture'];
            const currentIndex = modelTypes.indexOf(currentModelType);
            const nextIndex = (currentIndex + 1) % modelTypes.length;
            currentModelType = modelTypes[nextIndex];

            updateModelTypeDisplay();
        });
    }

    // Améliorations pour l'input du chat
    if (chatbotInput) {
        // Redimensionnement automatique du textarea
        chatbotInput.addEventListener('input', function() {
            updateCharCount();
            autoResizeTextarea();
        });

        // Support pour Shift+Enter pour nouvelle ligne
        chatbotInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendChatMessage();
            }
        });

        // Focus automatique quand le chat s'ouvre
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (!chatbotContainer.classList.contains('hidden')) {
                        setTimeout(() => chatbotInput.focus(), 100);
                    }
                }
            });
        });

        if (chatbotContainer) {
            observer.observe(chatbotContainer, { attributes: true });
        }
    }

    // Fonction pour mettre à jour l'affichage du type de modèle
    function updateModelTypeDisplay() {
        const currentModelText = document.getElementById('currentModelText');
        const modelTypeBtn = document.getElementById('modelTypeBtn');

        if (currentModelText && modelTypeBtn) {
            const modelInfo = {
                'brain': { text: 'Cerveau', icon: 'fas fa-brain' },
                'oral': { text: 'Dentaire', icon: 'fas fa-tooth' },
                'alzheimer': { text: 'Alzheimer', icon: 'fas fa-head-side-brain' },
                'fracture': { text: 'Fracture', icon: 'fas fa-bone' }
            };

            const info = modelInfo[currentModelType] || modelInfo['brain'];
            currentModelText.textContent = info.text;

            const icon = modelTypeBtn.querySelector('i');
            if (icon) {
                icon.className = info.icon;
            }
        }
    }

    // Initialiser l'affichage du type de modèle
    updateModelTypeDisplay();

    // Gestionnaires d'événements existants améliorés
    if (fileUpload) fileUpload.addEventListener('change', handleFiles, false);
    if (newAnalysisBtn) newAnalysisBtn.addEventListener('click', resetAnalysis);
    if (downloadPdfBtn) downloadPdfBtn.addEventListener('click', downloadPdfReport);
    if (analyzeBtn) analyzeBtn.addEventListener('click', analyzeImage);
    if (mobileMenuBtn) mobileMenuBtn.addEventListener('click', toggleMobileMenu);

    // Gestionnaire pour le bouton de connexion chat
    const connectChatBtn = document.getElementById('connectChatBtn');
    if (connectChatBtn) {
        connectChatBtn.addEventListener('click', function() {
            // Connecter l'analyse au chat
            if (resultData) {
                connectAnalysisToChat(resultData, { timestamp: new Date().toISOString() });

                // Ouvrir le chatbot
                if (chatbotContainer && chatbotContainer.classList.contains('hidden')) {
                    toggleChatbot();
                }

                // Afficher l'info de connexion
                const chatConnectionInfo = document.getElementById('chatConnectionInfo');
                if (chatConnectionInfo) {
                    chatConnectionInfo.classList.remove('hidden');
                }

                // Changer l'apparence du bouton
                this.innerHTML = '<i class="fas fa-check mr-2"></i> Analyse connectée';
                this.classList.add('opacity-75');
                this.disabled = true;
            }
        });
    }

    if (chatbotBtn) {
        chatbotBtn.addEventListener('click', function() {
            toggleChatbot();
        });
    }
    if (closeChatbot) closeChatbot.addEventListener('click', toggleChatbot);
    if (sendChatbotMsg) sendChatbotMsg.addEventListener('click', sendChatMessage);

    // Gestionnaire d'événements pour l'input du chat déjà géré plus haut

    window.addEventListener('scroll', toggleBackToTop);
    if (backToTop) backToTop.addEventListener('click', scrollToTop);



});

   </script>

   <!-- Script des améliorations du chat -->
   <script src="/static/js/chat-enhancements.js"></script>

   <!-- Scripts pour les effets de navigation -->
   <script>
       // Effet de scroll pour la navbar
       window.addEventListener('scroll', function() {
           const nav = document.getElementById('mainNav');
           const scrollProgress = document.querySelector('.scroll-progress-bar');

           if (window.scrollY > 50) {
               nav.classList.add('scrolled');
           } else {
               nav.classList.remove('scrolled');
           }

           // Barre de progression de scroll
           const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
           const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
           const scrolled = (winScroll / height) * 100;
           scrollProgress.style.width = scrolled + '%';
       });

       // Smooth scroll pour les liens de navigation
       document.querySelectorAll('a[href^="#"]').forEach(anchor => {
           anchor.addEventListener('click', function (e) {
               e.preventDefault();
               const target = document.querySelector(this.getAttribute('href'));
               if (target) {
                   target.scrollIntoView({
                       behavior: 'smooth',
                       block: 'start'
                   });
               }
           });
       });

       // Animation d'apparition au scroll
       const observerOptions = {
           threshold: 0.1,
           rootMargin: '0px 0px -50px 0px'
       };

       const observer = new IntersectionObserver(function(entries) {
           entries.forEach(entry => {
               if (entry.isIntersecting) {
                   entry.target.style.opacity = '1';
                   entry.target.style.transform = 'translateY(0)';
               }
           });
       }, observerOptions);

       // Observer les éléments avec animation
       document.querySelectorAll('.hover-lift, .medical-card, .glass-card').forEach(el => {
           el.style.opacity = '0';
           el.style.transform = 'translateY(20px)';
           el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
           observer.observe(el);
       });

       // Effet de parallax léger pour le hero
       window.addEventListener('scroll', function() {
           const scrolled = window.pageYOffset;
           const parallax = document.querySelector('.hero-professional');
           if (parallax) {
               const speed = scrolled * 0.5;
               parallax.style.transform = `translateY(${speed}px)`;
           }
       });

       // Animation des particules
       function animateParticles() {
           const particles = document.querySelectorAll('.particle');
           particles.forEach((particle, index) => {
               const delay = index * 0.5;
               particle.style.animationDelay = delay + 's';
           });
       }

       // Initialiser les animations
       document.addEventListener('DOMContentLoaded', function() {
           animateParticles();

           // Animation du logo au chargement
           const logo = document.querySelector('.nav-professional .group');
           if (logo) {
               setTimeout(() => {
                   logo.classList.add('animate-pulse');
                   setTimeout(() => {
                       logo.classList.remove('animate-pulse');
                   }, 2000);
               }, 1000);
           }
       });

       // Interactions pour le footer enterprise
       document.addEventListener('DOMContentLoaded', function() {
           // Animation des particules du footer enterprise
           const footerParticlesEnterprise = document.querySelectorAll('.footer-particle-enterprise');
           footerParticlesEnterprise.forEach((particle, index) => {
               const delay = index * 2;
               particle.style.animationDelay = delay + 's';
           });

           // Animation des compteurs
           function animateCounter(element, target, duration = 2000) {
               let start = 0;
               const increment = target / (duration / 16);
               const timer = setInterval(() => {
                   start += increment;
                   if (start >= target) {
                       element.textContent = target;
                       clearInterval(timer);
                   } else {
                       element.textContent = Math.floor(start);
                   }
               }, 16);
           }

           // Observer pour déclencher les animations des compteurs
           const counterObserver = new IntersectionObserver((entries) => {
               entries.forEach(entry => {
                   if (entry.isIntersecting) {
                       const counter = entry.target;
                       const target = parseInt(counter.getAttribute('data-target'));
                       if (target) {
                           animateCounter(counter, target);
                           counterObserver.unobserve(counter);
                       }
                   }
               });
           }, { threshold: 0.5 });

           // Observer tous les compteurs
           document.querySelectorAll('.counter').forEach(counter => {
               counterObserver.observe(counter);
           });

           // Newsletter subscription
           const newsletterForm = document.querySelector('.footer-enterprise input[type="email"]');
           const newsletterBtn = document.querySelector('.footer-enterprise button');

           if (newsletterBtn) {
               newsletterBtn.addEventListener('click', function(e) {
                   e.preventDefault();
                   const email = newsletterForm.value;

                   if (email && email.includes('@')) {
                       // Animation de succès
                       newsletterBtn.innerHTML = '<i class="fas fa-check"></i>';
                       newsletterBtn.classList.add('bg-green-500');

                       // Notification toast
                       showToast('Inscription réussie ! Merci de votre intérêt.', 'success');

                       // Reset après 3 secondes
                       setTimeout(() => {
                           newsletterBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
                           newsletterBtn.classList.remove('bg-green-500');
                           newsletterForm.value = '';
                       }, 3000);
                   } else {
                       showToast('Veuillez entrer une adresse email valide.', 'error');
                   }
               });
           }

           // Fonction pour afficher les notifications toast
           function showToast(message, type = 'info') {
               const toast = document.createElement('div');
               toast.className = `fixed top-4 right-4 z-50 p-4 rounded-xl shadow-xl transition-all duration-300 transform translate-x-full`;

               if (type === 'success') {
                   toast.classList.add('bg-green-500', 'text-white');
               } else if (type === 'error') {
                   toast.classList.add('bg-red-500', 'text-white');
               } else {
                   toast.classList.add('bg-blue-500', 'text-white');
               }

               toast.innerHTML = `
                   <div class="flex items-center space-x-2">
                       <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                       <span>${message}</span>
                   </div>
               `;

               document.body.appendChild(toast);

               // Animation d'entrée
               setTimeout(() => {
                   toast.classList.remove('translate-x-full');
               }, 100);

               // Animation de sortie
               setTimeout(() => {
                   toast.classList.add('translate-x-full');
                   setTimeout(() => {
                       document.body.removeChild(toast);
                   }, 300);
               }, 3000);
           }

           // Effet de hover sur les liens sociaux
           const socialLinks = document.querySelectorAll('.social-link');
           socialLinks.forEach(link => {
               link.addEventListener('mouseenter', function() {
                   this.style.transform = 'translateY(-4px) scale(1.1)';
               });

               link.addEventListener('mouseleave', function() {
                   this.style.transform = 'translateY(0) scale(1)';
               });
           });

           // Animation des statistiques au scroll
           const statsObserver = new IntersectionObserver((entries) => {
               entries.forEach(entry => {
                   if (entry.isIntersecting) {
                       const statNumbers = entry.target.querySelectorAll('.text-2xl');
                       statNumbers.forEach(stat => {
                           const finalValue = stat.textContent;
                           const isNumber = finalValue.match(/\d+/);

                           if (isNumber) {
                               const number = parseInt(isNumber[0]);
                               animateNumber(stat, 0, number, finalValue);
                           }
                       });
                   }
               });
           }, { threshold: 0.5 });

           const statsContainer = document.querySelector('.footer-professional .grid.grid-cols-3');
           if (statsContainer) {
               statsObserver.observe(statsContainer);
           }

           // Animation des nombres
           function animateNumber(element, start, end, suffix) {
               const duration = 2000;
               const startTime = performance.now();

               function update(currentTime) {
                   const elapsed = currentTime - startTime;
                   const progress = Math.min(elapsed / duration, 1);

                   const current = Math.floor(start + (end - start) * easeOutQuart(progress));
                   element.textContent = suffix.replace(/\d+/, current);

                   if (progress < 1) {
                       requestAnimationFrame(update);
                   }
               }

               requestAnimationFrame(update);
           }

           // Fonction d'easing
           function easeOutQuart(t) {
               return 1 - Math.pow(1 - t, 4);
           }
       });
   </script>
</body>
</html>
